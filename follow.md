# Advanced LiveKit Room Controls - Complete Implementation Guide

## Enhanced Database Schema

```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  rooms: defineTable({
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    isActive: v.boolean(),
    maxParticipants: v.optional(v.number()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    })),
    layout: v.optional(v.union(
      v.literal("grid"),
      v.literal("speaker"),
      v.literal("sidebar"),
      v.literal("floating")
    )),
    recordingSettings: v.optional(v.object({
      autoRecord: v.boolean(),
      recordAudio: v.boolean(),
      recordVideo: v.boolean(),
      recordScreen: v.boolean(),
    })),
  }).index("by_name", ["name"]).index("by_host", ["hostId"]),
  
  streams: defineTable({
    title: v.string(),
    description: v.string(),
    hostId: v.string(),
    isLive: v.boolean(),
    isChatEnabled: v.boolean(),
    isChatDelayed: v.boolean(),
    isChatFollowersOnly: v.boolean(),
    maxParticipants: v.optional(v.number()),
    streamKey: v.optional(v.string()),
  }).index("by_host", ["hostId"]),
  
  users: defineTable({
    userId: v.string(),
    globalRole: v.union(v.literal("master"), v.literal("admin"), v.literal("user")),
    username: v.string(),
    email: v.string(),
    isBanned: v.boolean(),
  }).index("by_user_id", ["userId"]),
  
  streamParticipants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
    joinedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_stream", ["streamId"])
    .index("by_user_stream", ["userId", "streamId"]),

  roomParticipants: defineTable({
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    ),
    permissions: v.object({
      canSpeak: v.boolean(),
      canVideo: v.boolean(),
      canScreenShare: v.boolean(),
      canChat: v.boolean(),
      canInvite: v.boolean(),
      canMute: v.boolean(),
      canKick: v.boolean(),
    }),
    status: v.union(
      v.literal("waiting"),
      v.literal("approved"),
      v.literal("denied"),
      v.literal("active"),
      v.literal("muted"),
      v.literal("kicked")
    ),
    handRaised: v.boolean(),
    joinedAt: v.number(),
    lastSeen: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_user_room", ["userId", "roomId"]),

  roomActions: defineTable({
    roomId: v.id("rooms"),
    performedBy: v.string(),
    targetUserId: v.optional(v.string()),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("muteAll"),
      v.literal("unmuteAll"),
      v.literal("kick"),
      v.literal("promote"),
      v.literal("demote"),
      v.literal("allowSpeak"),
      v.literal("denySpeak"),
      v.literal("startRecording"),
      v.literal("stopRecording"),
      v.literal("changeLayout"),
      v.literal("lockRoom"),
      v.literal("unlockRoom")
    ),
    metadata: v.optional(v.any()),
    timestamp: v.number(),
  }).index("by_room", ["roomId"]),
  
  moderationLogs: defineTable({
    streamId: v.id("streams"),
    moderatorId: v.string(),
    targetUserId: v.string(),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("timeout"),
      v.literal("kick"),
      v.literal("ban")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
    timestamp: v.number(),
  })
    .index("by_stream", ["streamId"])
    .index("by_target", ["targetUserId"]),
});
```

## Enhanced Convex Functions

### Room Management Functions

```typescript
// convex/rooms.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { AccessToken } from "livekit-server-sdk";

export const createRoom = mutation({
  args: {
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    // Create room in Convex
    const roomId = await ctx.db.insert("rooms", {
      ...args,
      isActive: true,
      maxParticipants: 50,
      layout: "grid",
      recordingSettings: {
        autoRecord: false,
        recordAudio: true,
        recordVideo: true,
        recordScreen: false,
      },
    });

    // Generate LiveKit token
    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY!,
      process.env.LIVEKIT_API_SECRET!,
      {
        identity: args.hostId,
        name: args.title,
      }
    );

    token.addGrant({
      room: args.name,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    return {
      roomId,
      token: token.toJwt(),
      livekitUrl: process.env.LIVEKIT_URL!,
    };
  },
});

export const getRoom = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.roomId);
  },
});

export const updateRoomSettings = mutation({
  args: {
    roomId: v.id("rooms"),
    settings: v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.roomId, { settings: args.settings });
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system", // You'd get this from auth
      action: "changeSettings",
      metadata: args.settings,
      timestamp: Date.now(),
    });
  },
});

export const muteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    duration: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Update participant status
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { status: "muted" });
    }
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system", // From auth
      targetUserId: args.userId,
      action: "mute",
      metadata: { duration: args.duration },
      timestamp: Date.now(),
    });
  },
});

export const muteAll = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    // Get all active participants except hosts
    const participants = await ctx.db.query("roomParticipants")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("role"), "host"))
      .collect();
    
    // Mute all participants
    await Promise.all(
      participants.map(p => 
        ctx.db.patch(p._id, { status: "muted" })
      )
    );
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      action: "muteAll",
      timestamp: Date.now(),
    });
  },
});

export const changeLayout = mutation({
  args: {
    roomId: v.id("rooms"),
    layout: v.union(v.literal("grid"), v.literal("speaker"), v.literal("sidebar"), v.literal("floating")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.roomId, { layout: args.layout });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      action: "changeLayout",
      metadata: { layout: args.layout },
      timestamp: Date.now(),
    });
  },
});

export const toggleRecording = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) throw new Error("Room not found");
    
    const newRecordingState = !room.recordingSettings?.autoRecord;
    
    await ctx.db.patch(args.roomId, {
      recordingSettings: {
        ...room.recordingSettings,
        autoRecord: newRecordingState,
      },
    });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      action: newRecordingState ? "startRecording" : "stopRecording",
      timestamp: Date.now(),
    });
  },
});

export const toggleRoomLock = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) throw new Error("Room not found");
    
    const newLockState = !room.settings?.requireAuth;
    
    await ctx.db.patch(args.roomId, {
      settings: {
        ...room.settings,
        requireAuth: newLockState,
      },
    });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      action: newLockState ? "lockRoom" : "unlockRoom",
      timestamp: Date.now(),
    });
  },
});

export const approveHandRaise = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, {
        handRaised: false,
        permissions: {
          ...participant.permissions,
          canSpeak: true,
          canVideo: true,
        },
      });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      targetUserId: args.userId,
      action: "allowSpeak",
      timestamp: Date.now(),
    });
  },
});

export const kickParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { status: "kicked" });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      targetUserId: args.userId,
      action: "kick",
      timestamp: Date.now(),
    });
  },
});

export const promoteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    ),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { role: args.role });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: "system",
      targetUserId: args.userId,
      action: "promote",
      metadata: { newRole: args.role },
      timestamp: Date.now(),
    });
  },
});
```

### Participant Management Functions

```typescript
// convex/participants.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const joinRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.optional(v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    )),
  },
  handler: async (ctx, args) => {
    // Check if user is already in room
    const existing = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => 
        q.eq("userId", args.userId).eq("roomId", args.roomId)
      )
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, { 
        status: "active",
        lastSeen: Date.now() 
      });
      return existing._id;
    }

    const role = args.role || "attendee";
    const permissions = getRolePermissions(role);

    // Generate LiveKit token
    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY!,
      process.env.LIVEKIT_API_SECRET!,
      {
        identity: args.userId,
      }
    );

    token.addGrant({
      room: args.roomId,
      roomJoin: true,
      canPublish: permissions.canSpeak,
      canSubscribe: true,
    });

    const participantId = await ctx.db.insert("roomParticipants", {
      roomId: args.roomId,
      userId: args.userId,
      role,
      permissions,
      status: "active",
      handRaised: false,
      joinedAt: Date.now(),
      lastSeen: Date.now(),
    });

    return {
      participantId,
      token: token.toJwt(),
      livekitUrl: process.env.LIVEKIT_URL!,
    };
  },
});

export const getRoomParticipants = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    return await ctx.db.query("roomParticipants")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .collect();
  },
});

export const raiseHand = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { handRaised: true });
    }
  },
});

export const lowerHand = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { handRaised: false });
    }
  },
});

// Helper function for role permissions
function getRolePermissions(role: string) {
  switch (role) {
    case 'host':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: true,
        canMute: true,
        canKick: true,
      };
    case 'co-host':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: true,
        canMute: true,
        canKick: true,
      };
    case 'moderator':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: false,
        canMute: true,
        canKick: false,
      };
    case 'speaker':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
      };
    default: // attendee
      return {
        canSpeak: false,
        canVideo: false,
        canScreenShare: false,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
      };
  }
}
```

### Stream Management Functions

```typescript
// convex/streams.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createStream = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    hostId: v.string(),
    multiStreamTargets: v.optional(v.array(v.object({
      platform: v.string(),
      rtmpUrl: v.string(),
      streamKey: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    // Create LivePeer stream
    const livepeerResponse = await fetch('https://livepeer.studio/api/stream', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.LIVEPEER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: args.title,
        multistream: args.multiStreamTargets ? {
          targets: args.multiStreamTargets.map(target => ({
            profile: 'source',
            videoOnly: false,
            spec: {
              url: `${target.rtmpUrl}/${target.streamKey}`,
              name: target.platform,
            }
          }))
        } : undefined,
      }),
    });

    const livepeerStream = await livepeerResponse.json();

    // Store in Convex
    const streamId = await ctx.db.insert("streams", {
      title: args.title,
      description: args.description,
      hostId: args.hostId,
      isLive: false,
      isChatEnabled: true,
      isChatDelayed: false,
      isChatFollowersOnly: false,
      streamKey: livepeerStream.streamKey,
    });

    return {
      streamId,
      livepeerStreamId: livepeerStream.id,
      streamKey: livepeerStream.streamKey,
      rtmpUrl: `rtmp://rtmp.livepeer.studio/live/${livepeerStream.streamKey}`,
    };
  },
});

export const getStream = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.streamId);
  },
});

export const updateStreamStatus = mutation({
  args: {
    streamId: v.id("streams"),
    isLive: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.streamId, {
      isLive: args.isLive,
    });

    // Notify all participants
    return await ctx.db.query("streamParticipants")
      .withIndex("by_stream", (q) => q.eq("streamId", args.streamId))
      .collect();
  },
});

export const getActiveStreams = query({
  handler: async (ctx) => {
    return await ctx.db.query("streams")
      .filter((q) => q.eq(q.field("isLive"), true))
      .collect();
  },
});

export const joinStream = mutation({
  args: {
    streamId: v.id("streams"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
  },
  handler: async (ctx, args) => {
    // Check if user is already in stream
    const existing = await ctx.db.query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.userId).eq("streamId", args.streamId)
      )
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, { isActive: true });
      return existing._id;
    }

    return await ctx.db.insert("streamParticipants", {
      streamId: args.streamId,
      userId: args.userId,
      role: args.role,
      joinedAt: Date.now(),
      isActive: true,
    });
  },
});

export const getStreamParticipants = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    return await ctx.db.query("streamParticipants")
      .withIndex("by_stream", (q) => q.eq("streamId", args.streamId))
      .collect();
  },
});
```

## Advanced Room Controls Component

```tsx
// components/room/RoomControls.tsx
'use client';

import { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  Monitor, 
  Users, 
  Settings,
  Record,
  Layout,
  Lock,
  Unlock,
  UserPlus,
  Hand,
  Volume2,
  VolumeX
} from 'lucide-react';

interface RoomControlsProps {
  roomId: Id<'rooms'>;
  userId: string;
  isHost: boolean;
  isModerator: boolean;
}

export default function RoomControls({ roomId, userId, isHost, isModerator }: RoomControlsProps) {
  const room = useQuery(api.rooms.getRoom, { roomId });
  const participants = useQuery(api.participants.getRoomParticipants, { roomId });
  const [showSettings, setShowSettings] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [currentLayout, setCurrentLayout] = useState<'grid' | 'speaker' | 'sidebar' | 'floating'>('grid');
  const [isRecording, setIsRecording] = useState(false);
  const [isRoomLocked, setIsRoomLocked] = useState(false);

  // Mutations
  const updateRoomSettings = useMutation(api.rooms.updateRoomSettings);
  const muteParticipant = useMutation(api.rooms.muteParticipant);
  const kickParticipant = useMutation(api.rooms.kickParticipant);
  const promoteParticipant = useMutation(api.rooms.promoteParticipant);
  const changeLayout = useMutation(api.rooms.changeLayout);
  const toggleRecording = useMutation(api.rooms.toggleRecording);
  const toggleRoomLock = useMutation(api.rooms.toggleRoomLock);
  const muteAll = useMutation(api.rooms.muteAll);
  const approveHandRaise = useMutation(api.rooms.approveHandRaise);

  const canControl = isHost || isModerator;

  const handleMuteAll = async () => {
    if (!canControl) return;
    await muteAll({ roomId });
  };

  const handleLayoutChange = async (layout: 'grid' | 'speaker' | 'sidebar' | 'floating') => {
    if (!canControl) return;
    await changeLayout({ roomId, layout });
    setCurrentLayout(layout);
  };

  const handleRecordingToggle = async () => {
    if (!canControl) return;
    await toggleRecording({ roomId });
    setIsRecording(!isRecording);
  };

  const handleRoomLockToggle = async () => {
    if (!isHost) return;
    await toggleRoomLock({ roomId });
    setIsRoomLocked(!isRoomLocked);
  };

  const handleParticipantAction = async (participantId: string, action: string) => {
    if (!canControl) return;
    
    switch (action) {
      case 'mute':
        await muteParticipant({ roomId, userId: participantId });
        break;
      case 'kick':
        await kickParticipant({ roomId, userId: participantId });
        break;
      case 'promote':
        await promoteParticipant({ roomId, userId: participantId, role: 'moderator' });
        break;
      case 'demote':
        await promoteParticipant({ roomId, userId: participantId, role: 'attendee' });
        break;
    }
  };

  const raisedHands = participants?.filter(p => p.handRaised) || [];
  const waitingParticipants = participants?.filter(p => p.status === 'waiting') || [];

  return (
    <div className="bg-white border-t border-gray-200 p-4">
      {/* Main Control Bar */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {/* Basic Controls */}
          <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-2">
            <button className="p-2 hover:bg-gray-200 rounded-md">
              <Mic className="w-5 h-5" />
            </button>
            <button className="p-2 hover:bg-gray-200 rounded-md">
              <Video className="w-5 h-5" />
            </button>
            <button className="p-2 hover:bg-gray-200 rounded-md">
              <Monitor className="w-5 h-5" />
            </button>
          </div>

          {/* Moderator Controls */}
          {canControl && (
            <div className="flex items-center gap-2 bg-blue-50 rounded-lg p-2">
              <button 
                onClick={handleMuteAll}
                className="p-2 hover:bg-blue-100 rounded-md text-blue-600"
                title="Mute All"
              >
                <VolumeX className="w-5 h-5" />
              </button>
              
              <button 
                onClick={handleRecordingToggle}
                className={`p-2 rounded-md ${isRecording ? 'bg-red-100 text-red-600' : 'hover:bg-blue-100 text-blue-600'}`}
                title={isRecording ? 'Stop Recording' : 'Start Recording'}
              >
                <Record className="w-5 h-5" />
              </button>

              {isHost && (
                <button 
                  onClick={handleRoomLockToggle}
                  className={`p-2 rounded-md ${isRoomLocked ? 'bg-orange-100 text-orange-600' : 'hover:bg-blue-100 text-blue-600'}`}
                  title={isRoomLocked ? 'Unlock Room' : 'Lock Room'}
                >
                  {isRoomLocked ? <Lock className="w-5 h-5" /> : <Unlock className="w-5 h-5" />}
                </button>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Notifications */}
          {raisedHands.length > 0 && (
            <div className="flex items-center gap-1 bg-yellow-100
          {raisedHands.length > 0 && (
            <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
              <Hand className="w-4 h-4" />
              {raisedHands.length}
            </div>
          )}

          {waitingParticipants.length > 0 && (
            <div className="flex items-center gap-1 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
              <UserPlus className="w-4 h-4" />
              {waitingParticipants.length}
            </div>
          )}

          {/* Action Buttons */}
          <button
            onClick={() => setShowParticipants(!showParticipants)}
            className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg"
          >
            <Users className="w-4 h-4" />
            {participants?.length || 0}
          </button>

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Layout Controls */}
      {canControl && (
        <div className="flex items-center gap-2 mb-4">
          <span className="text-sm font-medium text-gray-700">Layout:</span>
          <div className="flex gap-1">
            {['grid', 'speaker', 'sidebar', 'floating'].map((layout) => (
              <button
                key={layout}
                onClick={() => handleLayoutChange(layout as any)}
                className={`px-3 py-1 rounded-md text-sm ${
                  currentLayout === layout 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {layout.charAt(0).toUpperCase() + layout.slice(1)}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Participants Panel */}
      {showParticipants && (
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h3 className="font-medium mb-3">Participants ({participants?.length || 0})</h3>
          
          {/* Waiting for Approval */}
          {waitingParticipants.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Waiting for Approval</h4>
              <div className="space-y-2">
                {waitingParticipants.map((participant) => (
                  <div key={participant._id} className="flex items-center justify-between p-2 bg-yellow-50 rounded-md">
                    <span className="text-sm">{participant.userId}</span>
                    {isHost && (
                      <div className="flex gap-2">
                        <button 
                          onClick={() => approveHandRaise({ roomId, userId: participant.userId })}
                          className="px-2 py-1 bg-green-600 text-white rounded text-xs"
                        >
                          Approve
                        </button>
                        <button 
                          onClick={() => handleParticipantAction(participant.userId, 'kick')}
                          className="px-2 py-1 bg-red-600 text-white rounded text-xs"
                        >
                          Deny
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Raised Hands */}
          {raisedHands.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Raised Hands</h4>
              <div className="space-y-2">
                {raisedHands.map((participant) => (
                  <div key={participant._id} className="flex items-center justify-between p-2 bg-blue-50 rounded-md">
                    <div className="flex items-center gap-2">
                      <Hand className="w-4 h-4 text-blue-600" />
                      <span className="text-sm">{participant.userId}</span>
                    </div>
                    {canControl && (
                      <button 
                        onClick={() => approveHandRaise({ roomId, userId: participant.userId })}
                        className="px-2 py-1 bg-blue-600 text-white rounded text-xs"
                      >
                        Allow to Speak
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Active Participants */}
          <div className="space-y-2">
            {participants?.filter(p => p.status === 'active').map((participant) => (
              <div key={participant._id} className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${participant.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`} />
                  <span className="text-sm">{participant.userId}</span>
                  <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                    {participant.role}
                  </span>
                </div>
                
                {canControl && participant.userId !== userId && (
                  <div className="flex gap-1">
                    <button 
                      onClick={() => handleParticipantAction(participant.userId, 'mute')}
                      className="p-1 hover:bg-gray-200 rounded"
                      title="Mute"
                    >
                      <MicOff className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={() => handleParticipantAction(participant.userId, 'kick')}
                      className="p-1 hover:bg-gray-200 rounded text-red-600"
                      title="Remove"
                    >
                      <UserPlus className="w-4 h-4 rotate-45" />
                    </button>
                    {isHost && (
                      <button 
                        onClick={() => handleParticipantAction(
                          participant.userId, 
                          participant.role === 'moderator' ? 'demote' : 'promote'
                        )}
                        className="p-1 hover:bg-gray-200 rounded text-blue-600"
                        title={participant.role === 'moderator' ? 'Demote' : 'Promote to Moderator'}
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium mb-3">Room Settings</h3>
          <div className="grid grid-cols-2 gap-4">
            <label className="flex items-center gap-2">
              <input 
                type="checkbox" 
                defaultChecked={room?.settings?.allowScreenShare}
                className="rounded"
              />
              <span className="text-sm">Allow Screen Share</span>
            </label>
            <label className="flex items-center gap-2">
              <input 
                type="checkbox" 
                defaultChecked={room?.settings?.allowChat}
                className="rounded"
              />
              <span className="text-sm">Allow Chat</span>
            </label>
            <label className="flex items-center gap-2">
              <input 
                type="checkbox" 
                defaultChecked={room?.settings?.muteOnEntry}
                className="rounded"
              />
              <span className="text-sm">Mute on Entry</span>
            </label>
            <label className="flex items-center gap-2">
              <input 
                type="checkbox" 
                defaultChecked={room?.settings?.waitingRoom}
                className="rounded"
              />
              <span className="text-sm">Waiting Room</span>
            </label>
          </div>
        </div>
      )}
    </div>
  );
}
```

## Enhanced LiveKit Room Component

```tsx
// components/room/EnhancedLiveKitRoom.tsx
'use client';

import { LiveKitRoom, RoomAudioRenderer, useRoomContext } from '@livekit/components-react';
import { Room } from 'livekit-client';
import { useEffect, useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import RoomControls from './RoomControls';
import VideoGrid from './VideoGrid';
import WaitingRoom from './WaitingRoom';
import ChatPanel from './ChatPanel';

interface EnhancedLiveKitRoomProps {
  roomId: Id<'rooms'>;
  userId: string;
  userRole: string;
}

export default function EnhancedLiveKitRoom({ 
  roomId, 
  userId, 
  userRole 
}: EnhancedLiveKitRoomProps) {
  const room = useQuery(api.rooms.getRoom, { roomId });
  const [livekitRoom, setLivekitRoom] = useState<Room | null>(null);
  const [isApproved, setIsApproved] = useState(userRole === 'host' || userRole === 'co-host');
  const [token, setToken] = useState<string>('');
  const [serverUrl, setServerUrl] = useState<string>('');

  useEffect(() => {
    if (room && isApproved) {
      // Get token for room access
      fetch('/api/livekit-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roomId: roomId,
          userId: userId,
          role: userRole,
        }),
      })
      .then(res => res.json())
      .then(data => {
        setToken(data.token);
        setServerUrl(data.serverUrl);
      });
    }
  }, [room, isApproved, roomId, userId, userRole]);

  if (!room) {
    return <div className="flex items-center justify-center h-screen">Loading room...</div>;
  }

  if (!isApproved && room.settings?.waitingRoom) {
    return (
      <WaitingRoom 
        roomId={roomId} 
        userId={userId} 
        onApproved={() => setIsApproved(true)} 
      />
    );
  }

  if (!token || !serverUrl) {
    return <div className="flex items-center justify-center h-screen">Connecting...</div>;
  }

  return (
    <LiveKitRoom
      token={token}
      serverUrl={serverUrl}
      data-lk-theme="default"
      style={{ height: '100vh' }}
      onConnected={(room) => setLivekitRoom(room)}
      onDisconnected={() => setLivekitRoom(null)}
    >
      <div className="flex h-full">
        {/* Main Video Area */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 relative">
            <VideoGrid layout={room.layout || 'grid'} />
            <RoomAudioRenderer />
          </div>
          
          <RoomControls 
            roomId={roomId}
            userId={userId}
            isHost={userRole === 'host'}
            isModerator={userRole === 'moderator' || userRole === 'co-host'}
          />
        </div>

        {/* Chat Sidebar */}
        {room.settings?.allowChat && (
          <div className="w-80 border-l border-gray-200">
            <ChatPanel roomId={roomId} userId={userId} />
          </div>
        )}
      </div>
    </LiveKitRoom>
  );
}
```

## Video Grid Component

```tsx
// components/room/VideoGrid.tsx
'use client';

import { useParticipants, useTracks } from '@livekit/components-react';
import { Track } from 'livekit-client';

interface VideoGridProps {
  layout: 'grid' | 'speaker' | 'sidebar' | 'floating';
}

export default function VideoGrid({ layout }: VideoGridProps) {
  const participants = useParticipants();
  const tracks = useTracks([Track.Source.Camera, Track.Source.ScreenShare]);

  const getGridClass = () => {
    switch (layout) {
      case 'grid':
        return `grid gap-2 p-4 ${
          participants.length <= 4 ? 'grid-cols-2' :
          participants.length <= 9 ? 'grid-cols-3' :
          'grid-cols-4'
        }`;
      case 'speaker':
        return 'flex flex-col h-full';
      case 'sidebar':
        return 'flex h-full';
      case 'floating':
        return 'relative h-full p-4';
      default:
        return 'grid grid-cols-2 gap-2 p-4';
    }
  };

  if (layout === 'speaker') {
    const activeSpeaker = participants.find(p => p.isSpeaking) || participants[0];
    const otherParticipants = participants.filter(p => p !== activeSpeaker);

    return (
      <div className="flex flex-col h-full">
        {/* Main Speaker */}
        <div className="flex-1 p-4">
          {activeSpeaker && (
            <div className="w-full h-full bg-black rounded-lg relative">
              <video
                className="w-full h-full object-cover rounded-lg"
                autoPlay
                playsInline
                muted={activeSpeaker.isLocal}
                ref={(ref) => {
                  if (ref && activeSpeaker.videoTracks.size > 0) {
                    const track = Array.from(activeSpeaker.videoTracks.values())[0];
                    track.track?.attach(ref);
                  }
                }}
              />
              <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded">
                {activeSpeaker.name || activeSpeaker.identity}
              </div>
            </div>
          )}
        </div>
        
        {/* Thumbnail Strip */}
        {otherParticipants.length > 0 && (
          <div className="h-24 flex gap-2 p-4 overflow-x-auto">
            {otherParticipants.map((participant) => (
              <div key={participant.sid} className="flex-shrink-0 w-32 h-full bg-black rounded relative">
                <video
                  className="w-full h-full object-cover rounded"
                  autoPlay
                  playsInline
                  muted={participant.isLocal}
                  ref={(ref) => {
                    if (ref && participant.videoTracks.size > 0) {
                      const track = Array.from(participant.videoTracks.values())[0];
                      track.track?.attach(ref);
                    }
                  }}
                />
                <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                  {participant.name || participant.identity}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={getGridClass()}>
      {participants.map((participant) => (
        <div key={participant.sid} className="relative bg-black rounded-lg overflow-hidden">
          <video
            className="w-full h-full object-cover"
            autoPlay
            playsInline
            muted={participant.isLocal}
            ref={(ref) => {
              if (ref && participant.videoTracks.size > 0) {
                const track = Array.from(participant.videoTracks.values())[0];
                track.track?.attach(ref);
              }
            }}
          />
          
          {/* Participant Info */}
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
            {participant.name || participant.identity}
          </div>
          
          {/* Audio/Video Status */}
          <div className="absolute top-2 right-2 flex gap-1">
            {participant.isMicrophoneEnabled ? (
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <Mic className="w-3 h-3 text-white" />
              </div>
            ) : (
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <MicOff className="w-3 h-3 text-white" />
              </div>
            )}
            
            {!participant.isCameraEnabled && (
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <VideoOff className="w-3 h-3 text-white" />
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
```

## Waiting Room Component

```tsx
// components/room/WaitingRoom.tsx
'use client';

import { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { Clock, Users } from 'lucide-react';

interface WaitingRoomProps {
  roomId: Id<'rooms'>;
  userId: string;
  onApproved: () => void;
}

export default function WaitingRoom({ roomId, userId, onApproved }: WaitingRoomProps) {
  const room = useQuery(api.rooms.getRoom, { roomId });
  const participant = useQuery(api.participants.getParticipant, { roomId, userId });
  const [timeWaiting, setTimeWaiting] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeWaiting(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (participant?.status === 'approved') {
      onApproved();
    }
  }, [participant?.status, onApproved]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Clock className="w-8 h-8 text-blue-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Waiting for Approval</h2>
          <p className="text-gray-600">
            You're in the waiting room for "{room?.title}". The host will let you in soon.
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>Waiting: {formatTime(timeWaiting)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Room: {room?.name}</span>
            </div>
          </div>
        </div>

        <div className="space-y-3 text-sm text-gray-500">
          <p>• Please keep this window open</p>
          <p>• You'll automatically join when approved</p>
          <p>• The host has been notified of your request</p>
        </div>

        <div className="mt-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## Chat Panel Component

```tsx
// components/room/ChatPanel.tsx
'use client';

import { useState, useEffect, useRef } from 'react';
import { Send, MoreVertical } from 'lucide-react';
import { Id } from '../../convex/_generated/dataModel';

interface ChatPanelProps {
  roomId: Id<'rooms'>;
  userId: string;
}

interface ChatMessage {
  id: string;
  userId: string;
  message: string;
  timestamp: number;
  type: 'message' | 'system';
}

export default function ChatPanel({ roomId, userId }: ChatPanelProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      userId,
      message: newMessage,
      timestamp: Date.now(),
      type: 'message',
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Chat</h3>
        <button className="p-1 hover:bg-gray-100 rounded">
          <MoreVertical className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.map((message) => (
          <div key={message.id} className="group">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {message.userId.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-baseline gap-2">
                  <span className="font-medium text-gray-900 text-sm">
                    {message.userId === userId ? 'You' : message.userId}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatTime(message.timestamp)}
                  </span>
                </div>
                <p className="text-gray-700 text-sm mt-1 break-words">
                  {message.message}
                </p>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={1}
              style={{ maxHeight: '100px' }}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Permission System

```typescript
// lib/permissions.ts
export interface RoomPermissions {
  canSpeak: boolean;
  canVideo: boolean;
  canScreenShare: boolean;
  canChat: boolean;
  canInvite: boolean;
  canMute: boolean;
  canKick: boolean;
  canManageWaitingRoom: boolean;
  canChangeLayout: boolean;
  canRecord: boolean;
}

export const getRolePermissions = (role: string): RoomPermissions => {
  switch (role) {
    case 'host':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: true,
        canMute: true,
        canKick: true,
        canManageWaitingRoom: true,
        canChangeLayout: true,
        canRecord: true,
      };
    case 'co-host':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: true,
        canMute: true,
        canKick: true,
        canManageWaitingRoom: true,
        canChangeLayout: true,
        canRecord: false,
      };
    case 'moderator':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: false,
        canMute: true,
        canKick: false,
        canManageWaitingRoom: true,
        canChangeLayout: false,
        canRecord: false,
      };
    case 'speaker':
      return {
        canSpeak: true,
        canVideo: true,
        canScreenShare: true,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
        canManageWaitingRoom: false,
        canChangeLayout: false,
        canRecord: false,
      };
    default: // attendee
      return {
        canSpeak: false,
        canVideo: false,
        canScreenShare: false,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
        canManageWaitingRoom: false,
        canChangeLayout: false,
        canRecord: false,
      };
  }
};

export const canPerformAction = (
  userRole: string,
  action: keyof RoomPermissions
): boolean => {
  const permissions = getRolePermissions(userRole);
  return permissions[action];
};
```

## Custom Hooks

```typescript
// hooks/useStream.ts
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

export const useStream = (streamId: Id<'streams'>) => {
  const stream = useQuery(api.streams.getStream, { streamId });
  const participants = useQuery(api.streams.getStreamParticipants, { streamId });
  const updateStatus = useMutation(api.streams.updateStreamStatus);
  const joinStream = useMutation(api.streams.joinStream);

  const startStream = async () => {
    await updateStatus({ streamId, isLive: true });
  };

  const stopStream = async () => {
    await updateStatus({ streamId, isLive: false });
  };

  const join = async (userId: string, role: 'host' | 'co-host' | 'moderator' | 'guest' | 'viewer') => {
    await joinStream({ streamId, userId, role });
  };

  return {
    stream,
    participants,
    startStream,
    stopStream,
    join,
    isLive: stream?.isLive ?? false,
  };
};
```

```typescript
// hooks/useRoom.ts
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

export const useRoom = (roomId: Id<'rooms'>) => {
  const room = useQuery(api.rooms.getRoom, { roomId });
  const participants = useQuery(api.participants.getRoomParticipants, { roomId });
  
  const joinRoom = useMutation(api.participants.joinRoom);
  const muteParticipant = useMutation(api.rooms.muteParticipant);
  const kickParticipant = useMutation(api.rooms.kickParticipant);
  const raiseHand = useMutation(api.participants.raiseHand);
  const lowerHand = useMutation(api.participants.lowerHand);

  const join = async (userId: string, role?: string) => {
    return await joinRoom({ roomId, userId, role });
  };

  const mute = async (userId: string) => {
    await muteParticipant({ roomId, userId });
  };

  const kick = async (userId: string) => {
    await kickParticipant({ roomId, userId });
  };

  const requestToSpeak = async (userId: string) => {
    await raiseHand({ roomId, userId });
  };

  const cancelSpe