"use client";

import React from 'react';
import { Id } from '../../convex/_generated/dataModel';

interface BreakoutRoomManagerProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function BreakoutRoomManager({ streamId, currentUserId }: BreakoutRoomManagerProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Breakout Room Manager</h2>
      <p className="text-gray-600">Breakout room features coming soon...</p>
    </div>
  );
}