"use client";

import React from 'react';
import { Id } from '../../convex/_generated/dataModel';

interface ModerationPanelProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function ModerationPanel({ streamId, currentUserId }: ModerationPanelProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Moderation Panel</h2>
      <p className="text-gray-600">Moderation features coming soon...</p>
    </div>
  );
}