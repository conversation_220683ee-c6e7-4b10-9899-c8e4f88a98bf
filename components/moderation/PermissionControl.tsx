"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface PermissionControlProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function PermissionControl({ streamId, currentUserId }: PermissionControlProps) {
  const stream = useQuery(api.streams.get, { streamId });
  const participants = useQuery(api.participants.getStreamParticipants, { streamId });
  const updateStream = useMutation(api.streams.updateStream);

  const [globalSettings, setGlobalSettings] = useState({
    isChatEnabled: stream?.isChatEnabled ?? true,
    isChatDelayed: stream?.isChatDelayed ?? false,
    isChatFollowersOnly: stream?.isChatFollowersOnly ?? false,
  });

  const handleGlobalSettingChange = async (setting: keyof typeof globalSettings, value: boolean) => {
    const newSettings = { ...globalSettings, [setting]: value };
    setGlobalSettings(newSettings);
    
    try {
      await updateStream({
        streamId,
        [setting]: value,
      });
    } catch (error) {
      console.error('Failed to update stream settings:', error);
    }
  };

  const handleParticipantPermissionChange = async (
    userId: string,
    permission: string,
    value: boolean
  ) => {
    try {
      // This would update participant permissions
      console.log('Updating participant permission:', { userId, permission, value });
    } catch (error) {
      console.error('Failed to update participant permissions:', error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold mb-4">Permission Control</h3>
      
      {/* Global Stream Settings */}
      <div className="mb-6">
        <h4 className="font-medium mb-3">Global Stream Settings</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Enable Chat</label>
              <p className="text-sm text-gray-500">Participants can send chat messages</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.isChatEnabled}
                onChange={(e) => handleGlobalSettingChange('isChatEnabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Chat Delay</label>
              <p className="text-sm text-gray-500">Add delay to chat messages</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.isChatDelayed}
                onChange={(e) => handleGlobalSettingChange('isChatDelayed', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Followers Only Chat</label>
              <p className="text-sm text-gray-500">Only followers can chat</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.isChatFollowersOnly}
                onChange={(e) => handleGlobalSettingChange('isChatFollowersOnly', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Individual Participant Permissions */}
      <div>
        <h4 className="font-medium mb-3">Individual Permissions</h4>
        <div className="space-y-3">
          {participants?.filter(p => p.role !== 'host').map((participant) => (
            <div key={participant._id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h5 className="font-medium">{participant.user?.username || 'Anonymous'}</h5>
                  <span className={`text-xs px-2 py-1 rounded ${
                    participant.role === 'moderator' ? 'bg-blue-100 text-blue-800' :
                    participant.role === 'co-host' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {participant.role}
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Speak</span>
                  <input
                    type="checkbox"
                    defaultChecked={participant.role !== 'viewer'}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant.userId, 
                      'canSpeak', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Video</span>
                  <input
                    type="checkbox"
                    defaultChecked={participant.role !== 'viewer'}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant.userId, 
                      'canVideo', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Screen Share</span>
                  <input
                    type="checkbox"
                    defaultChecked={participant.role === 'host' || participant.role === 'co-host'}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant.userId, 
                      'canScreenShare', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Chat</span>
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant.userId, 
                      'canChat', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}