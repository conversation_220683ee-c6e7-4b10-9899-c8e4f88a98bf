"use client";

import { useUser, UserButton } from "@clerk/nextjs";
import Link from "next/link";

export function Header() {
  const { isSignedIn } = useUser();

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <span className="font-bold">StreamYard Clone</span>
          </Link>
        </div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <nav className="flex items-center space-x-6">
            {isSignedIn && (
              <>
                <Link
                  href="/create"
                  className="text-sm font-medium transition-colors hover:text-primary"
                >
                  Create Stream
                </Link>
              </>
            )}
          </nav>
          <div className="flex items-center space-x-2">
            {isSignedIn ? (
              <UserButton afterSignOutUrl="/" />
            ) : (
              <Link
                href="/sign-in"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                Sign In
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}