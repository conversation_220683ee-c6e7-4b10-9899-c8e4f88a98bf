"use client";

import React from 'react';
import {
  LiveKitRoom,
  VideoConference,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
} from '@livekit/components-react';
import { Track } from 'livekit-client';
import '@livekit/components-styles';

interface VideoPlayerProps {
  token: string;
  room: string;
  children?: React.ReactNode;
}

function CustomVideoConference() {
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  return (
    <div className="h-full flex flex-col">
      <GridLayout tracks={tracks} style={{ height: '100%' }}>
        <ParticipantTile />
      </GridLayout>
    </div>
  );
}

export function VideoPlayer({ token, room, children }: VideoPlayerProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://streamyard-clonez-1zofz2li.livekit.cloud';

  return (
    <div className="w-full h-full bg-black rounded-lg overflow-hidden">
      <LiveKitRoom
        video={true}
        audio={true}
        token={token}
        serverUrl={serverUrl}
        data-lk-theme="default"
        style={{ height: '100%' }}
        onConnected={() => console.log('Connected to room:', room)}
        onDisconnected={() => console.log('Disconnected from room:', room)}
        onError={(error) => console.error('LiveKit error:', error)}
      >
        <CustomVideoConference />
        <RoomAudioRenderer />
        {children}
      </LiveKitRoom>
    </div>
  );
}