"use client";

import React, { useState } from 'react';
import { 
  UserPlus, 
  Mail, 
  Users, 
  Eye, 
  EyeOff, 
  Mic, 
  Mic<PERSON>ff, 
  Video, 
  VideoOff,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  Copy,
  ExternalLink,
  Settings,
  Crown,
  Shield,
  User,
  Trash2
} from 'lucide-react';

interface Guest {
  id: string;
  name: string;
  email: string;
  status: 'invited' | 'pending' | 'backstage' | 'on-stage' | 'left';
  role: 'guest' | 'moderator' | 'co-host';
  joinedAt?: Date;
  isAudioMuted: boolean;
  isVideoOff: boolean;
  isVisible: boolean;
}

interface GuestManagerProps {
  isOpen: boolean;
  onClose: () => void;
  guests: Guest[];
  onUpdateGuests: (guests: Guest[]) => void;
  streamId: string;
}

export function GuestManager({ isOpen, onClose, guests, onUpdateGuests, streamId }: GuestManagerProps) {
  const [activeTab, setActiveTab] = useState<'invite' | 'manage' | 'backstage'>('invite');
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<Guest['role']>('guest');
  const [inviteMessage, setInviteMessage] = useState('');
  const [selectedGuests, setSelectedGuests] = useState<string[]>([]);

  const handleInviteGuest = () => {
    if (!inviteEmail.trim()) return;

    const newGuest: Guest = {
      id: `guest-${Date.now()}`,
      name: inviteEmail.split('@')[0],
      email: inviteEmail,
      status: 'invited',
      role: inviteRole,
      isAudioMuted: false,
      isVideoOff: false,
      isVisible: true,
    };

    onUpdateGuests([...guests, newGuest]);
    setInviteEmail('');
    setInviteMessage('');
    
    // Here you would send the actual invitation email
    console.log('Sending invitation to:', inviteEmail);
  };

  const updateGuestStatus = (guestId: string, status: Guest['status']) => {
    const updatedGuests = guests.map(guest =>
      guest.id === guestId ? { ...guest, status } : guest
    );
    onUpdateGuests(updatedGuests);
  };

  const updateGuestSettings = (guestId: string, updates: Partial<Guest>) => {
    const updatedGuests = guests.map(guest =>
      guest.id === guestId ? { ...guest, ...updates } : guest
    );
    onUpdateGuests(updatedGuests);
  };

  const removeGuest = (guestId: string) => {
    onUpdateGuests(guests.filter(guest => guest.id !== guestId));
  };

  const moveToStage = (guestId: string) => {
    updateGuestStatus(guestId, 'on-stage');
  };

  const moveToBackstage = (guestId: string) => {
    updateGuestStatus(guestId, 'backstage');
  };

  const generateInviteLink = () => {
    return `${window.location.origin}/join/${streamId}`;
  };

  const copyInviteLink = () => {
    navigator.clipboard.writeText(generateInviteLink());
    // Show toast notification
  };

  if (!isOpen) return null;

  const pendingGuests = guests.filter(g => g.status === 'pending');
  const backstageGuests = guests.filter(g => g.status === 'backstage');
  const onStageGuests = guests.filter(g => g.status === 'on-stage');

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Guest Management</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          {[
            { id: 'invite', label: 'Invite Guests', icon: UserPlus },
            { id: 'manage', label: 'Manage Guests', icon: Users },
            { id: 'backstage', label: 'Backstage', icon: Eye },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center space-x-2 px-6 py-3 transition-colors ${
                activeTab === id 
                  ? 'bg-blue-600 text-white border-b-2 border-blue-400' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
              {id === 'backstage' && backstageGuests.length > 0 && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {backstageGuests.length}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'invite' && (
            <div className="space-y-6">
              {/* Invite Link */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-3">Share Invite Link</h3>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={generateInviteLink()}
                    readOnly
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  />
                  <button
                    onClick={copyInviteLink}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Copy</span>
                  </button>
                </div>
                <p className="text-sm text-gray-400 mt-2">
                  Anyone with this link can request to join your stream
                </p>
              </div>

              {/* Email Invitation */}
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-3">Send Email Invitation</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={inviteEmail}
                        onChange={(e) => setInviteEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Role
                      </label>
                      <select
                        value={inviteRole}
                        onChange={(e) => setInviteRole(e.target.value as Guest['role'])}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"
                      >
                        <option value="guest">Guest</option>
                        <option value="moderator">Moderator</option>
                        <option value="co-host">Co-Host</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Personal Message (Optional)
                    </label>
                    <textarea
                      value={inviteMessage}
                      onChange={(e) => setInviteMessage(e.target.value)}
                      placeholder="Add a personal message to your invitation..."
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"
                    />
                  </div>
                  
                  <button
                    onClick={handleInviteGuest}
                    disabled={!inviteEmail.trim()}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Send className="w-4 h-4" />
                    <span>Send Invitation</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'manage' && (
            <div className="space-y-4">
              {/* On Stage Guests */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3 flex items-center space-x-2">
                  <span>On Stage</span>
                  <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    {onStageGuests.length}
                  </span>
                </h3>
                <div className="space-y-2">
                  {onStageGuests.map((guest) => (
                    <div key={guest.id} className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {guest.role === 'co-host' && <Crown className="w-4 h-4 text-yellow-500" />}
                            {guest.role === 'moderator' && <Shield className="w-4 h-4 text-blue-500" />}
                            {guest.role === 'guest' && <User className="w-4 h-4 text-gray-400" />}
                          </div>
                          <div>
                            <p className="text-white font-medium">{guest.name}</p>
                            <p className="text-sm text-gray-400">{guest.email}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateGuestSettings(guest.id, { isAudioMuted: !guest.isAudioMuted })}
                            className={`p-2 rounded transition-colors ${
                              guest.isAudioMuted 
                                ? 'bg-red-600 text-white' 
                                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            }`}
                          >
                            {guest.isAudioMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                          </button>
                          
                          <button
                            onClick={() => updateGuestSettings(guest.id, { isVideoOff: !guest.isVideoOff })}
                            className={`p-2 rounded transition-colors ${
                              guest.isVideoOff 
                                ? 'bg-red-600 text-white' 
                                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            }`}
                          >
                            {guest.isVideoOff ? <VideoOff className="w-4 h-4" /> : <Video className="w-4 h-4" />}
                          </button>
                          
                          <button
                            onClick={() => moveToBackstage(guest.id)}
                            className="px-3 py-1 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors text-sm"
                          >
                            Move to Backstage
                          </button>
                          
                          <button
                            onClick={() => removeGuest(guest.id)}
                            className="p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {onStageGuests.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      No guests on stage
                    </div>
                  )}
                </div>
              </div>

              {/* Pending Approval */}
              {pendingGuests.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-white mb-3 flex items-center space-x-2">
                    <span>Pending Approval</span>
                    <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full">
                      {pendingGuests.length}
                    </span>
                  </h3>
                  <div className="space-y-2">
                    {pendingGuests.map((guest) => (
                      <div key={guest.id} className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-white font-medium">{guest.name}</p>
                            <p className="text-sm text-gray-400">{guest.email}</p>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => updateGuestStatus(guest.id, 'backstage')}
                              className="flex items-center space-x-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                            >
                              <CheckCircle className="w-4 h-4" />
                              <span>Approve</span>
                            </button>
                            
                            <button
                              onClick={() => removeGuest(guest.id)}
                              className="flex items-center space-x-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            >
                              <XCircle className="w-4 h-4" />
                              <span>Deny</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'backstage' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-white">Backstage Area</h3>
                <p className="text-sm text-gray-400">
                  Guests can prepare here before going live
                </p>
              </div>
              
              <div className="space-y-2">
                {backstageGuests.map((guest) => (
                  <div key={guest.id} className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
                          <User className="w-6 h-6 text-gray-400" />
                        </div>
                        <div>
                          <p className="text-white font-medium">{guest.name}</p>
                          <p className="text-sm text-gray-400">Ready to go live</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => moveToStage(guest.id)}
                          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          <ExternalLink className="w-4 h-4" />
                          <span>Bring to Stage</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                
                {backstageGuests.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    No guests in backstage
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
