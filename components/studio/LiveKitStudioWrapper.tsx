"use client";

import React from 'react';
import { Live<PERSON>itR<PERSON>, RoomAudioRenderer } from '@livekit/components-react';
import '@livekit/components-styles';

interface LiveKitStudioWrapperProps {
  token: string;
  room: string;
  children: React.ReactNode;
}

export function LiveKitStudioWrapper({ token, room, children }: LiveKitStudioWrapperProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://streamyard-clonez-1zofz2li.livekit.cloud';

  return (
    <LiveKitRoom
      video={true}
      audio={true}
      token={token}
      serverUrl={serverUrl}
      data-lk-theme="default"
      onConnected={() => console.log('Connected to room:', room)}
      onDisconnected={() => console.log('Disconnected from room:', room)}
      onError={(error) => console.error('LiveKit error:', error)}
    >
      {children}
      <RoomAudioRenderer />
    </LiveKitRoom>
  );
}
