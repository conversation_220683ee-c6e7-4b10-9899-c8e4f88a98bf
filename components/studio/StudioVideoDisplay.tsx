"use client";

import React from 'react';
import {
  GridLayout,
  ParticipantTile,
  useTracks,
} from '@livekit/components-react';
import { Track } from 'livekit-client';
import { Layers, Image, Type } from 'lucide-react';
import { useLiveKitControls } from '../../hooks/useLiveKitControls';

interface StudioVideoDisplayProps {
  onShowBranding?: () => void;
}

export function StudioVideoDisplay({ onShowBranding }: StudioVideoDisplayProps) {
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  const { isRecording } = useLiveKitControls();

  return (
    <div className="w-full h-full bg-black rounded-lg overflow-hidden relative">
      <GridLayout tracks={tracks} style={{ height: '100%' }}>
        <ParticipantTile />
      </GridLayout>

      {/* Stage Overlay Controls */}
      <div className="absolute top-4 left-4 flex space-x-2">
        <button
          onClick={onShowBranding}
          className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
          title="Branding & Graphics"
        >
          <Layers className="w-4 h-4" />
        </button>
        <button
          onClick={onShowBranding}
          className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
          title="Add Logo"
        >
          <Image className="w-4 h-4" />
        </button>
        <button
          onClick={onShowBranding}
          className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
          title="Add Text"
        >
          <Type className="w-4 h-4" />
        </button>
      </div>

      {/* Recording Indicator */}
      {isRecording && (
        <div className="absolute top-4 right-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-lg">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">REC</span>
        </div>
      )}
    </div>
  );
}
