"use client";

import React from 'react';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff,
  ScreenShare,
  Circle,
  MessageCircle,
  Users,
  MoreHorizontal,
  Loader2,
  AlertCircle,
  X
} from 'lucide-react';
import { useLiveKitControls } from '../../hooks/useLiveKitControls';

interface LiveKitControlPanelProps {
  onToggleChat: () => void;
  onToggleParticipants: () => void;
  onShowMore: () => void;
  className?: string;
}

export function LiveKitControlPanel({ 
  onToggleChat, 
  onToggleParticipants, 
  onShowMore,
  className = ""
}: LiveKitControlPanelProps) {
  const {
    isMuted,
    isVideoOff,
    isScreenSharing,
    isRecording,
    isLoading,
    error,
    toggleMute,
    toggleVideo,
    toggleScreenShare,
    toggleRecording,
    clearError
  } = useLiveKitControls();

  const handleMuteClick = async () => {
    await toggleMute();
  };

  const handleVideoClick = async () => {
    await toggleVideo();
  };

  const handleScreenShareClick = async () => {
    await toggleScreenShare();
  };

  const handleRecordingClick = async () => {
    await toggleRecording();
  };

  return (
    <div className={`bg-gray-800 border-t border-gray-700 p-2 md:p-4 ${className}`}>
      {/* Error Banner */}
      {error && (
        <div className="mb-3 p-3 bg-red-900/50 border border-red-500/50 rounded-lg flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-400" />
            <span className="text-sm text-red-200">{error}</span>
          </div>
          <button
            onClick={clearError}
            className="p-1 text-red-400 hover:text-red-300 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      <div className="flex items-center justify-between">
        {/* Left Controls - Media */}
        <div className="flex items-center space-x-2 md:space-x-3">
          {/* Microphone Control */}
          <button
            onClick={handleMuteClick}
            disabled={isLoading.audio}
            className={`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${
              isMuted 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            title={isMuted ? 'Unmute microphone' : 'Mute microphone'}
          >
            {isLoading.audio ? (
              <Loader2 className="w-4 h-4 md:w-5 md:h-5 animate-spin" />
            ) : isMuted ? (
              <MicOff className="w-4 h-4 md:w-5 md:h-5" />
            ) : (
              <Mic className="w-4 h-4 md:w-5 md:h-5" />
            )}
          </button>
          
          {/* Camera Control */}
          <button
            onClick={handleVideoClick}
            disabled={isLoading.video}
            className={`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${
              isVideoOff 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
          >
            {isLoading.video ? (
              <Loader2 className="w-4 h-4 md:w-5 md:h-5 animate-spin" />
            ) : isVideoOff ? (
              <VideoOff className="w-4 h-4 md:w-5 md:h-5" />
            ) : (
              <Video className="w-4 h-4 md:w-5 md:h-5" />
            )}
          </button>
          
          {/* Screen Share Control */}
          <button
            onClick={handleScreenShareClick}
            disabled={isLoading.screenShare}
            className={`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${
              isScreenSharing 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            title={isScreenSharing ? 'Stop screen sharing' : 'Start screen sharing'}
          >
            {isLoading.screenShare ? (
              <Loader2 className="w-4 h-4 md:w-5 md:h-5 animate-spin" />
            ) : (
              <ScreenShare className="w-4 h-4 md:w-5 md:h-5" />
            )}
          </button>
        </div>
        
        {/* Center Controls - Recording */}
        <div className="flex items-center space-x-2 md:space-x-3">
          <button
            onClick={handleRecordingClick}
            disabled={isLoading.recording}
            className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-lg transition-colors text-sm disabled:opacity-50 ${
              isRecording 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            title={isRecording ? 'Stop recording' : 'Start recording'}
          >
            {isLoading.recording ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Circle className={`w-4 h-4 ${isRecording ? 'fill-current' : ''}`} />
            )}
            <span className="hidden sm:inline">
              {isRecording ? 'Stop Recording' : 'Record'}
            </span>
            <span className="sm:hidden">
              {isRecording ? 'Stop' : 'Rec'}
            </span>
          </button>
        </div>
        
        {/* Right Controls - UI */}
        <div className="flex items-center space-x-2 md:space-x-3">
          <button 
            onClick={onToggleChat}
            className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            title="Toggle chat"
          >
            <MessageCircle className="w-4 h-4 md:w-5 md:h-5" />
          </button>
          
          <button 
            onClick={onToggleParticipants}
            className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            title="Manage participants"
          >
            <Users className="w-4 h-4 md:w-5 md:h-5" />
          </button>
          
          <button 
            onClick={onShowMore}
            className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            title="More options"
          >
            <MoreHorizontal className="w-4 h-4 md:w-5 md:h-5" />
          </button>
        </div>
      </div>

      {/* Status Indicators */}
      <div className="flex items-center justify-center mt-2 space-x-4 text-xs text-gray-400">
        {isRecording && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>Recording</span>
          </div>
        )}
        {isScreenSharing && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Screen sharing</span>
          </div>
        )}
        {isMuted && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Muted</span>
          </div>
        )}
        {isVideoOff && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Camera off</span>
          </div>
        )}
      </div>
    </div>
  );
}
