"use client";

import React, { useState } from 'react';
import { 
  Grid3X3, 
  User, 
  Users, 
  Star, 
  Monitor, 
  Tv, 
  Film, 
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  Move,
  RotateCcw
} from 'lucide-react';

export interface Layout {
  id: string;
  name: string;
  type: 'preset' | 'custom';
  icon: React.ReactNode;
  description: string;
  config: {
    participants: number;
    arrangement: 'single' | 'grid' | 'spotlight' | 'pip' | 'side-by-side' | 'custom';
    showScreen: boolean;
    screenPosition?: 'main' | 'side' | 'overlay';
  };
}

const presetLayouts: Layout[] = [
  {
    id: 'single',
    name: 'Single',
    type: 'preset',
    icon: <User className="w-5 h-5" />,
    description: 'Highlights one participant',
    config: {
      participants: 1,
      arrangement: 'single',
      showScreen: false,
    }
  },
  {
    id: 'group',
    name: 'Group',
    type: 'preset',
    icon: <Users className="w-5 h-5" />,
    description: 'Grid layout for multiple participants',
    config: {
      participants: 4,
      arrangement: 'grid',
      showScreen: false,
    }
  },
  {
    id: 'spotlight',
    name: 'Spotlight',
    type: 'preset',
    icon: <Star className="w-5 h-5" />,
    description: 'Highlights main speaker with others below',
    config: {
      participants: 4,
      arrangement: 'spotlight',
      showScreen: false,
    }
  },
  {
    id: 'news',
    name: 'News',
    type: 'preset',
    icon: <Tv className="w-5 h-5" />,
    description: 'Side-by-side presenter and screen',
    config: {
      participants: 1,
      arrangement: 'side-by-side',
      showScreen: true,
      screenPosition: 'side',
    }
  },
  {
    id: 'screen',
    name: 'Screen',
    type: 'preset',
    icon: <Monitor className="w-5 h-5" />,
    description: 'Full screen share with small presenter',
    config: {
      participants: 1,
      arrangement: 'pip',
      showScreen: true,
      screenPosition: 'main',
    }
  },
  {
    id: 'cinema',
    name: 'Cinema',
    type: 'preset',
    icon: <Film className="w-5 h-5" />,
    description: 'Full screen presentation only',
    config: {
      participants: 0,
      arrangement: 'custom',
      showScreen: true,
      screenPosition: 'main',
    }
  },
];

interface LayoutManagerProps {
  selectedLayout: string;
  onLayoutChange: (layoutId: string) => void;
  onCreateCustomLayout: () => void;
  customLayouts?: Layout[];
}

export function LayoutManager({ 
  selectedLayout, 
  onLayoutChange, 
  onCreateCustomLayout,
  customLayouts = []
}: LayoutManagerProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingLayout, setEditingLayout] = useState<Layout | null>(null);

  const allLayouts = [...presetLayouts, ...customLayouts];

  const handleEditLayout = (layout: Layout) => {
    if (layout.type === 'custom') {
      setEditingLayout(layout);
      setIsEditMode(true);
    }
  };

  const handleDeleteLayout = (layoutId: string) => {
    // Implementation for deleting custom layouts
    console.log('Delete layout:', layoutId);
  };

  const handleSaveLayout = () => {
    // Implementation for saving layout changes
    setIsEditMode(false);
    setEditingLayout(null);
  };

  return (
    <div className="space-y-4">
      {/* Layout Grid */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        {allLayouts.map((layout) => (
          <div key={layout.id} className="relative group">
            <button
              onClick={() => onLayoutChange(layout.id)}
              className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all min-w-[80px] ${
                selectedLayout === layout.id
                  ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                  : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500'
              }`}
              title={layout.description}
            >
              <div className="mb-2">
                {layout.icon}
              </div>
              <span className="text-xs font-medium">{layout.name}</span>
            </button>
            
            {/* Custom Layout Controls */}
            {layout.type === 'custom' && (
              <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEditLayout(layout)}
                    className="p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                  >
                    <Edit3 className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleDeleteLayout(layout.id)}
                    className="p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
        
        {/* Create Custom Layout Button */}
        <button
          onClick={onCreateCustomLayout}
          className="flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 text-gray-400 hover:border-gray-500 hover:text-gray-300 transition-colors min-w-[80px]"
        >
          <Plus className="w-5 h-5 mb-2" />
          <span className="text-xs font-medium">Custom</span>
        </button>
      </div>

      {/* Layout Info */}
      {selectedLayout && (
        <div className="bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-white">
                {allLayouts.find(l => l.id === selectedLayout)?.name}
              </h4>
              <p className="text-xs text-gray-400">
                {allLayouts.find(l => l.id === selectedLayout)?.description}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-white transition-colors">
                <Move className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white transition-colors">
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Mode Modal */}
      {isEditMode && editingLayout && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Edit Layout</h3>
              <button
                onClick={() => setIsEditMode(false)}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Layout Name
                </label>
                <input
                  type="text"
                  value={editingLayout.name}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <input
                  type="text"
                  value={editingLayout.description}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsEditMode(false)}
                  className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveLayout}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
