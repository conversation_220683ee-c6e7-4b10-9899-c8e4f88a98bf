"use client";

import React, { useState } from 'react';
import { 
  Image, 
  Type, 
  Palette, 
  Upload, 
  X, 
  Move, 
  RotateCcw, 
  Eye, 
  EyeOff,
  Layers,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Plus,
  Trash2,
  Copy,
  Settings
} from 'lucide-react';

interface BrandElement {
  id: string;
  type: 'logo' | 'text' | 'banner' | 'background' | 'overlay';
  content: string;
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
  zIndex: number;
  style: {
    fontSize?: number;
    fontWeight?: string;
    color?: string;
    backgroundColor?: string;
    opacity?: number;
    borderRadius?: number;
    textAlign?: 'left' | 'center' | 'right';
    fontFamily?: string;
  };
}

interface BrandingPanelProps {
  isOpen: boolean;
  onClose: () => void;
  elements: BrandElement[];
  onUpdateElements: (elements: BrandElement[]) => void;
}

export function BrandingPanel({ isOpen, onClose, elements, onUpdateElements }: BrandingPanelProps) {
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'logos' | 'text' | 'banners' | 'backgrounds'>('logos');

  const addElement = (type: BrandElement['type']) => {
    const newElement: BrandElement = {
      id: `element-${Date.now()}`,
      type,
      content: type === 'text' ? 'Sample Text' : '',
      x: 50,
      y: 50,
      width: type === 'text' ? 200 : 100,
      height: type === 'text' ? 40 : 100,
      visible: true,
      zIndex: elements.length,
      style: {
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        backgroundColor: type === 'banner' ? '#000000' : 'transparent',
        opacity: 1,
        borderRadius: 0,
        textAlign: 'left',
        fontFamily: 'Arial',
      },
    };
    
    onUpdateElements([...elements, newElement]);
    setSelectedElement(newElement.id);
  };

  const updateElement = (id: string, updates: Partial<BrandElement>) => {
    const updatedElements = elements.map(el => 
      el.id === id ? { ...el, ...updates } : el
    );
    onUpdateElements(updatedElements);
  };

  const updateElementStyle = (id: string, styleUpdates: Partial<BrandElement['style']>) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      updateElement(id, {
        style: { ...element.style, ...styleUpdates }
      });
    }
  };

  const deleteElement = (id: string) => {
    onUpdateElements(elements.filter(el => el.id !== id));
    if (selectedElement === id) {
      setSelectedElement(null);
    }
  };

  const duplicateElement = (id: string) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      const newElement = {
        ...element,
        id: `element-${Date.now()}`,
        x: element.x + 20,
        y: element.y + 20,
      };
      onUpdateElements([...elements, newElement]);
    }
  };

  const toggleVisibility = (id: string) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      updateElement(id, { visible: !element.visible });
    }
  };

  if (!isOpen) return null;

  const selectedEl = elements.find(el => el.id === selectedElement);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Branding & Graphics</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex-1 flex">
          {/* Sidebar */}
          <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Tabs */}
            <div className="flex border-b border-gray-700">
              {[
                { id: 'logos', label: 'Logos', icon: Image },
                { id: 'text', label: 'Text', icon: Type },
                { id: 'banners', label: 'Banners', icon: Layers },
                { id: 'backgrounds', label: 'Backgrounds', icon: Palette },
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 p-3 transition-colors ${
                    activeTab === id 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-400 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm">{label}</span>
                </button>
              ))}
            </div>

            {/* Content */}
            <div className="flex-1 p-4 overflow-y-auto">
              {activeTab === 'logos' && (
                <div className="space-y-4">
                  <button
                    onClick={() => addElement('logo')}
                    className="w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Upload Logo</span>
                  </button>
                  
                  <div className="text-sm text-gray-400">
                    Recommended: PNG with transparent background, 300x300px max
                  </div>
                </div>
              )}

              {activeTab === 'text' && (
                <div className="space-y-4">
                  <button
                    onClick={() => addElement('text')}
                    className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Text</span>
                  </button>

                  {selectedEl?.type === 'text' && (
                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">Text Content</label>
                        <input
                          type="text"
                          value={selectedEl.content}
                          onChange={(e) => updateElement(selectedEl.id, { content: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">Font Size</label>
                          <input
                            type="number"
                            value={selectedEl.style.fontSize}
                            onChange={(e) => updateElementStyle(selectedEl.id, { fontSize: parseInt(e.target.value) })}
                            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">Color</label>
                          <input
                            type="color"
                            value={selectedEl.style.color}
                            onChange={(e) => updateElementStyle(selectedEl.id, { color: e.target.value })}
                            className="w-full h-8 bg-gray-700 border border-gray-600 rounded"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs text-gray-400 mb-1">Font Weight</label>
                        <select
                          value={selectedEl.style.fontWeight}
                          onChange={(e) => updateElementStyle(selectedEl.id, { fontWeight: e.target.value })}
                          className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                        >
                          <option value="normal">Normal</option>
                          <option value="bold">Bold</option>
                          <option value="lighter">Light</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs text-gray-400 mb-1">Alignment</label>
                        <div className="flex space-x-1">
                          {[
                            { value: 'left', icon: AlignLeft },
                            { value: 'center', icon: AlignCenter },
                            { value: 'right', icon: AlignRight },
                          ].map(({ value, icon: Icon }) => (
                            <button
                              key={value}
                              onClick={() => updateElementStyle(selectedEl.id, { textAlign: value as any })}
                              className={`flex-1 p-2 rounded transition-colors ${
                                selectedEl.style.textAlign === value
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              <Icon className="w-4 h-4 mx-auto" />
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'banners' && (
                <div className="space-y-4">
                  <button
                    onClick={() => addElement('banner')}
                    className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Banner</span>
                  </button>
                  
                  <div className="text-sm text-gray-400">
                    Create scrolling text banners and lower thirds
                  </div>
                </div>
              )}

              {activeTab === 'backgrounds' && (
                <div className="space-y-4">
                  <button
                    onClick={() => addElement('background')}
                    className="w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Upload Background</span>
                  </button>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="aspect-video bg-gradient-to-br from-blue-500 to-purple-600 rounded cursor-pointer"></div>
                    <div className="aspect-video bg-gradient-to-br from-green-500 to-blue-500 rounded cursor-pointer"></div>
                    <div className="aspect-video bg-gradient-to-br from-purple-500 to-pink-500 rounded cursor-pointer"></div>
                    <div className="aspect-video bg-gradient-to-br from-orange-500 to-red-500 rounded cursor-pointer"></div>
                  </div>
                </div>
              )}
            </div>

            {/* Element List */}
            <div className="border-t border-gray-700 p-4">
              <h3 className="text-sm font-medium text-white mb-2">Elements</h3>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {elements.map((element) => (
                  <div
                    key={element.id}
                    className={`flex items-center justify-between p-2 rounded transition-colors cursor-pointer ${
                      selectedElement === element.id 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                    onClick={() => setSelectedElement(element.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-xs">{element.type}</span>
                      <span className="text-xs opacity-75 truncate max-w-20">
                        {element.content || 'Untitled'}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleVisibility(element.id);
                        }}
                        className="p-1 hover:bg-gray-600 rounded"
                      >
                        {element.visible ? (
                          <Eye className="w-3 h-3" />
                        ) : (
                          <EyeOff className="w-3 h-3" />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteElement(element.id);
                        }}
                        className="p-1 hover:bg-red-600 rounded"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Preview Area */}
          <div className="flex-1 p-4">
            <div className="w-full h-full bg-black rounded-lg relative overflow-hidden">
              <div className="absolute inset-4 border border-dashed border-gray-600 rounded">
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Layers className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Preview your branding elements</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
