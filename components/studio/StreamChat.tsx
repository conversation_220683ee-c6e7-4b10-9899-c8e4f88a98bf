"use client";

import React, { useState, useRef, useEffect } from 'react';
import { 
  Send, 
  Smile, 
  Heart, 
  ThumbsUp, 
  Gift, 
  Shield, 
  Ban, 
  Trash2, 
  Pin, 
  MoreHorizontal,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Filter,
  Settings,
  Users,
  MessageCircle,
  Star,
  Flag
} from 'lucide-react';

interface ChatMessage {
  id: string;
  username: string;
  message: string;
  timestamp: Date;
  type: 'message' | 'system' | 'donation' | 'follow' | 'subscription';
  isHighlighted?: boolean;
  isPinned?: boolean;
  isModerator?: boolean;
  isOwner?: boolean;
  reactions?: { emoji: string; count: number }[];
  amount?: number; // for donations
}

interface StreamChatProps {
  streamId: string;
  currentUserId: string;
  isModerator: boolean;
  isVisible: boolean;
  onToggleVisibility: () => void;
}

export function StreamChat({ streamId, currentUserId, isModerator, isVisible, onToggleVisibility }: StreamChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isSlowMode, setIsSlowMode] = useState(false);
  const [slowModeDelay, setSlowModeDelay] = useState(5);
  const [isFollowersOnly, setIsFollowersOnly] = useState(false);
  const [bannedWords, setBannedWords] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'donations' | 'follows' | 'messages'>('all');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Sample messages for demonstration
  useEffect(() => {
    const sampleMessages: ChatMessage[] = [
      {
        id: '1',
        username: 'StreamFan123',
        message: 'Great stream! Love the content 🔥',
        timestamp: new Date(Date.now() - 300000),
        type: 'message',
        reactions: [{ emoji: '👍', count: 3 }, { emoji: '❤️', count: 1 }]
      },
      {
        id: '2',
        username: 'System',
        message: 'NewViewer just followed!',
        timestamp: new Date(Date.now() - 240000),
        type: 'follow'
      },
      {
        id: '3',
        username: 'GenerousViewer',
        message: 'Keep up the amazing work!',
        timestamp: new Date(Date.now() - 180000),
        type: 'donation',
        amount: 5.00,
        isHighlighted: true
      },
      {
        id: '4',
        username: 'Moderator',
        message: 'Welcome everyone to the stream!',
        timestamp: new Date(Date.now() - 120000),
        type: 'message',
        isModerator: true,
        isPinned: true
      }
    ];
    setMessages(sampleMessages);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      username: 'You',
      message: newMessage,
      timestamp: new Date(),
      type: 'message',
      isOwner: true
    };

    setMessages([...messages, message]);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const deleteMessage = (messageId: string) => {
    setMessages(messages.filter(msg => msg.id !== messageId));
  };

  const pinMessage = (messageId: string) => {
    setMessages(messages.map(msg => 
      msg.id === messageId ? { ...msg, isPinned: !msg.isPinned } : msg
    ));
  };

  const banUser = (username: string) => {
    // Implementation for banning user
    console.log('Banning user:', username);
  };

  const filteredMessages = messages.filter(msg => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'donations') return msg.type === 'donation';
    if (selectedFilter === 'follows') return msg.type === 'follow' || msg.type === 'subscription';
    if (selectedFilter === 'messages') return msg.type === 'message';
    return true;
  });

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getMessageIcon = (type: ChatMessage['type']) => {
    switch (type) {
      case 'donation': return <Gift className="w-4 h-4 text-yellow-500" />;
      case 'follow': return <Heart className="w-4 h-4 text-red-500" />;
      case 'subscription': return <Star className="w-4 h-4 text-purple-500" />;
      default: return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="w-5 h-5 text-blue-400" />
            <h3 className="font-semibold text-white">Stream Chat</h3>
            <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
              {messages.length} messages
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Filter Dropdown */}
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as any)}
              className="text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white"
            >
              <option value="all">All</option>
              <option value="messages">Messages</option>
              <option value="donations">Donations</option>
              <option value="follows">Follows</option>
            </select>
            
            {isModerator && (
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-1 text-gray-400 hover:text-white transition-colors"
              >
                <Settings className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={onToggleVisibility}
              className="p-1 text-gray-400 hover:text-white transition-colors"
            >
              {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Moderation Settings */}
        {showSettings && isModerator && (
          <div className="mt-3 p-3 bg-gray-800 rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Slow Mode</span>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={isSlowMode}
                  onChange={(e) => setIsSlowMode(e.target.checked)}
                  className="rounded"
                />
                {isSlowMode && (
                  <select
                    value={slowModeDelay}
                    onChange={(e) => setSlowModeDelay(Number(e.target.value))}
                    className="text-xs bg-gray-700 border border-gray-600 rounded px-1 py-1 text-white"
                  >
                    <option value={5}>5s</option>
                    <option value={10}>10s</option>
                    <option value={30}>30s</option>
                    <option value={60}>1m</option>
                  </select>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Followers Only</span>
              <input
                type="checkbox"
                checked={isFollowersOnly}
                onChange={(e) => setIsFollowersOnly(e.target.checked)}
                className="rounded"
              />
            </div>
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`group relative ${
              message.isHighlighted ? 'bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-2' : ''
            } ${message.isPinned ? 'bg-blue-500/10 border border-blue-500/30 rounded-lg p-2' : ''}`}
          >
            {message.isPinned && (
              <div className="flex items-center space-x-1 text-xs text-blue-400 mb-1">
                <Pin className="w-3 h-3" />
                <span>Pinned Message</span>
              </div>
            )}
            
            <div className="flex items-start space-x-2">
              <div className="flex-shrink-0">
                {getMessageIcon(message.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className={`text-sm font-medium ${
                    message.isOwner ? 'text-green-400' :
                    message.isModerator ? 'text-blue-400' :
                    'text-gray-300'
                  }`}>
                    {message.username}
                  </span>
                  
                  {message.isModerator && (
                    <Shield className="w-3 h-3 text-blue-400" />
                  )}
                  
                  {message.type === 'donation' && message.amount && (
                    <span className="text-xs bg-yellow-500 text-black px-2 py-1 rounded font-medium">
                      ${message.amount.toFixed(2)}
                    </span>
                  )}
                  
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(message.timestamp)}
                  </span>
                </div>
                
                <p className="text-sm text-gray-200 break-words">{message.message}</p>
                
                {message.reactions && message.reactions.length > 0 && (
                  <div className="flex items-center space-x-2 mt-2">
                    {message.reactions.map((reaction, index) => (
                      <button
                        key={index}
                        className="flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 rounded-full px-2 py-1 transition-colors"
                      >
                        <span>{reaction.emoji}</span>
                        <span className="text-gray-300">{reaction.count}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Message Actions */}
              {isModerator && (
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => pinMessage(message.id)}
                      className="p-1 text-gray-400 hover:text-blue-400 transition-colors"
                    >
                      <Pin className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => deleteMessage(message.id)}
                      className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => banUser(message.username)}
                      className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <Ban className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              maxLength={500}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
              <button className="p-1 text-gray-400 hover:text-yellow-400 transition-colors">
                <Smile className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
        
        {isSlowMode && (
          <p className="text-xs text-yellow-400 mt-2">
            Slow mode is enabled ({slowModeDelay}s between messages)
          </p>
        )}
      </div>
    </div>
  );
}
