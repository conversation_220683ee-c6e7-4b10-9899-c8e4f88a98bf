"use client";

import React, { useState, useRef } from 'react';
import { 
  Plus, 
  Move, 
  RotateCcw, 
  Save, 
  X, 
  User, 
  Monitor, 
  Grid3X3,
  Square,
  Circle,
  Maximize2,
  Minimize2,
  Copy,
  Trash2
} from 'lucide-react';

interface LayoutElement {
  id: string;
  type: 'camera' | 'screen' | 'media' | 'dynamic-grid';
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
  shape: 'rectangle' | 'circle' | 'rounded';
  fit: 'cover' | 'contain';
}

interface CustomLayoutBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (layout: any) => void;
  existingLayout?: any;
}

export function CustomLayoutBuilder({ 
  isOpen, 
  onClose, 
  onSave, 
  existingLayout 
}: CustomLayoutBuilderProps) {
  const [elements, setElements] = useState<LayoutElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [draggedElement, setDraggedElement] = useState<string | null>(null);
  const [layoutName, setLayoutName] = useState('');
  const [isEditMode, setIsEditMode] = useState(true);
  const stageRef = useRef<HTMLDivElement>(null);

  const addElement = (type: LayoutElement['type']) => {
    const newElement: LayoutElement = {
      id: `element-${Date.now()}`,
      type,
      x: 50,
      y: 50,
      width: type === 'dynamic-grid' ? 300 : 150,
      height: type === 'dynamic-grid' ? 200 : 100,
      zIndex: elements.length,
      shape: 'rectangle',
      fit: 'cover',
    };
    setElements([...elements, newElement]);
    setSelectedElement(newElement.id);
  };

  const updateElement = (id: string, updates: Partial<LayoutElement>) => {
    setElements(elements.map(el => 
      el.id === id ? { ...el, ...updates } : el
    ));
  };

  const deleteElement = (id: string) => {
    setElements(elements.filter(el => el.id !== id));
    if (selectedElement === id) {
      setSelectedElement(null);
    }
  };

  const duplicateElement = (id: string) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      const newElement = {
        ...element,
        id: `element-${Date.now()}`,
        x: element.x + 20,
        y: element.y + 20,
      };
      setElements([...elements, newElement]);
    }
  };

  const handleMouseDown = (e: React.MouseEvent, elementId: string) => {
    e.preventDefault();
    setSelectedElement(elementId);
    setDraggedElement(elementId);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!draggedElement || !stageRef.current) return;

    const rect = stageRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    updateElement(draggedElement, { x, y });
  };

  const handleMouseUp = () => {
    setDraggedElement(null);
  };

  const handleSave = () => {
    if (!layoutName.trim()) {
      alert('Please enter a layout name');
      return;
    }

    const layout = {
      id: `custom-${Date.now()}`,
      name: layoutName,
      type: 'custom' as const,
      elements,
      createdAt: Date.now(),
    };

    onSave(layout);
    onClose();
  };

  if (!isOpen) return null;

  const selectedEl = elements.find(el => el.id === selectedElement);

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-white">Custom Layout Builder</h2>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                placeholder="Layout name..."
                value={layoutName}
                onChange={(e) => setLayoutName(e.target.value)}
                className="px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-500 focus:outline-none"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsEditMode(!isEditMode)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                isEditMode 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {isEditMode ? 'Preview' : 'Edit'}
            </button>
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex-1 flex">
          {/* Toolbar */}
          {isEditMode && (
            <div className="w-64 bg-gray-800 border-r border-gray-700 p-4 space-y-4">
              <div>
                <h3 className="text-sm font-medium text-white mb-3">Add Elements</h3>
                <div className="space-y-2">
                  <button
                    onClick={() => addElement('camera')}
                    className="w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
                  >
                    <User className="w-4 h-4" />
                    <span>Camera Slot</span>
                  </button>
                  <button
                    onClick={() => addElement('dynamic-grid')}
                    className="w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
                  >
                    <Grid3X3 className="w-4 h-4" />
                    <span>Dynamic Grid</span>
                  </button>
                  <button
                    onClick={() => addElement('screen')}
                    className="w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
                  >
                    <Monitor className="w-4 h-4" />
                    <span>Media Slot</span>
                  </button>
                </div>
              </div>

              {/* Element Properties */}
              {selectedEl && (
                <div>
                  <h3 className="text-sm font-medium text-white mb-3">Properties</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Shape</label>
                      <select
                        value={selectedEl.shape}
                        onChange={(e) => updateElement(selectedEl.id, { shape: e.target.value as any })}
                        className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="rectangle">Rectangle</option>
                        <option value="rounded">Rounded</option>
                        <option value="circle">Circle</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Fit</label>
                      <select
                        value={selectedEl.fit}
                        onChange={(e) => updateElement(selectedEl.id, { fit: e.target.value as any })}
                        className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="cover">Fill</option>
                        <option value="contain">Fit</option>
                      </select>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => duplicateElement(selectedEl.id)}
                        className="flex-1 flex items-center justify-center space-x-1 p-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
                      >
                        <Copy className="w-3 h-3" />
                        <span className="text-xs">Copy</span>
                      </button>
                      <button
                        onClick={() => deleteElement(selectedEl.id)}
                        className="flex-1 flex items-center justify-center space-x-1 p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                      >
                        <Trash2 className="w-3 h-3" />
                        <span className="text-xs">Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Stage */}
          <div className="flex-1 p-4">
            <div 
              ref={stageRef}
              className="w-full h-full bg-black rounded-lg relative overflow-hidden border-2 border-dashed border-gray-600"
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              style={{ aspectRatio: '16/9' }}
            >
              {elements.map((element) => (
                <div
                  key={element.id}
                  className={`absolute border-2 transition-all cursor-move ${
                    selectedElement === element.id 
                      ? 'border-blue-500 bg-blue-500/20' 
                      : 'border-gray-500 bg-gray-500/20 hover:border-gray-400'
                  } ${
                    element.shape === 'circle' ? 'rounded-full' :
                    element.shape === 'rounded' ? 'rounded-lg' : ''
                  }`}
                  style={{
                    left: element.x,
                    top: element.y,
                    width: element.width,
                    height: element.height,
                    zIndex: element.zIndex,
                  }}
                  onMouseDown={(e) => handleMouseDown(e, element.id)}
                >
                  <div className="flex items-center justify-center h-full text-white text-sm">
                    {element.type === 'camera' && <User className="w-6 h-6" />}
                    {element.type === 'screen' && <Monitor className="w-6 h-6" />}
                    {element.type === 'dynamic-grid' && <Grid3X3 className="w-6 h-6" />}
                  </div>
                  
                  {/* Resize handles */}
                  {selectedElement === element.id && isEditMode && (
                    <>
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-se-resize"></div>
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize"></div>
                      <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize"></div>
                      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize"></div>
                    </>
                  )}
                </div>
              ))}
              
              {elements.length === 0 && (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Plus className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Add elements to create your layout</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
