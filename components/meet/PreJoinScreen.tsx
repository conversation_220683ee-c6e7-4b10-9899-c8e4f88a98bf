"use client";

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, VideoOff, Mic, Mic<PERSON>ff, Settings } from 'lucide-react';

interface PreJoinScreenProps {
  roomName: string;
  userName: string;
  onJoin: (displayName: string) => void;
}

export function PreJoinScreen({ roomName, userName, onJoin }: PreJoinScreenProps) {
  const [displayName, setDisplayName] = useState(userName);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);

  useEffect(() => {
    if (isVideoEnabled) {
      startPreview();
    } else {
      stopPreview();
    }

    return () => {
      stopPreview();
    };
  }, [isVideoEnabled]);

  const startPreview = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: isVideoEnabled,
        audio: isAudioEnabled
      });
      
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing media devices:', error);
    }
  };

  const stopPreview = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  const handleJoin = () => {
    stopPreview();
    onJoin(displayName || userName || 'Anonymous');
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Ready to join?</h1>
          <p className="text-gray-600">Meeting: {roomName}</p>
        </div>

        {/* Video Preview */}
        <div className="relative bg-gray-900 rounded-lg overflow-hidden mb-6 aspect-video">
          {isVideoEnabled ? (
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xl font-semibold">
                  {(displayName || userName || 'A')[0].toUpperCase()}
                </span>
              </div>
            </div>
          )}
          
          {/* Video Controls */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
            <button
              onClick={() => setIsVideoEnabled(!isVideoEnabled)}
              className={`p-3 rounded-full transition-colors ${
                isVideoEnabled 
                  ? 'bg-gray-600 hover:bg-gray-700' 
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              {isVideoEnabled ? (
                <Video className="w-5 h-5 text-white" />
              ) : (
                <VideoOff className="w-5 h-5 text-white" />
              )}
            </button>
            
            <button
              onClick={() => setIsAudioEnabled(!isAudioEnabled)}
              className={`p-3 rounded-full transition-colors ${
                isAudioEnabled 
                  ? 'bg-gray-600 hover:bg-gray-700' 
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              {isAudioEnabled ? (
                <Mic className="w-5 h-5 text-white" />
              ) : (
                <MicOff className="w-5 h-5 text-white" />
              )}
            </button>
          </div>
        </div>

        {/* Name Input */}
        <div className="mb-6">
          <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-2">
            Your name
          </label>
          <input
            id="displayName"
            type="text"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            placeholder="Enter your name"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
          />
        </div>

        {/* Join Button */}
        <button
          onClick={handleJoin}
          disabled={!displayName.trim()}
          className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Join meeting
        </button>

        {/* Settings hint */}
        <div className="mt-4 text-center">
          <button className="inline-flex items-center space-x-2 text-sm text-gray-500 hover:text-gray-700">
            <Settings className="w-4 h-4" />
            <span>Check your audio and video settings</span>
          </button>
        </div>
      </div>
    </div>
  );
}