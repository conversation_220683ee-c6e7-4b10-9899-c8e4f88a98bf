"use client";

import React, { useState } from 'react';
import { 
  <PERSON>, 
  User<PERSON>he<PERSON>, 
  UserX, 
  Clock,
  Shield,
  CheckCircle,
  XCircle,
  UserPlus,
  AlertCircle
} from 'lucide-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface WaitingRoomProps {
  roomId: Id<"rooms">;
  isHost: boolean;
  onClose: () => void;
}

interface WaitingParticipant {
  _id: Id<"roomParticipants">;
  userId: string;
  role: string;
  status: string;
  joinedAt: number;
  lastSeen: number;
}

export function WaitingRoom({ roomId, isHost, onClose }: WaitingRoomProps) {
  const [isAdmittingAll, setIsAdmittingAll] = useState(false);
  const [processingParticipants, setProcessingParticipants] = useState<Set<string>>(new Set());

  // Queries
  const waitingParticipants = useQuery(api.rooms.getWaitingParticipants, { roomId }) as WaitingParticipant[] | undefined;

  // Mutations
  const admitParticipant = useMutation(api.rooms.admitParticipant);
  const denyParticipant = useMutation(api.rooms.denyParticipant);
  const admitAllWaiting = useMutation(api.rooms.admitAllWaiting);

  const handleAdmitParticipant = async (userId: string) => {
    if (!isHost) return;
    
    setProcessingParticipants(prev => new Set(prev).add(userId));
    try {
      await admitParticipant({ roomId, userId });
    } catch (error) {
      console.error('Failed to admit participant:', error);
    } finally {
      setProcessingParticipants(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleDenyParticipant = async (userId: string) => {
    if (!isHost) return;
    
    setProcessingParticipants(prev => new Set(prev).add(userId));
    try {
      await denyParticipant({ roomId, userId });
    } catch (error) {
      console.error('Failed to deny participant:', error);
    } finally {
      setProcessingParticipants(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleAdmitAll = async () => {
    if (!isHost || !waitingParticipants?.length) return;
    
    setIsAdmittingAll(true);
    try {
      await admitAllWaiting({ roomId });
    } catch (error) {
      console.error('Failed to admit all participants:', error);
    } finally {
      setIsAdmittingAll(false);
    }
  };

  const formatWaitTime = (joinedAt: number) => {
    const waitTime = Math.floor((Date.now() - joinedAt) / 1000);
    if (waitTime < 60) return `${waitTime}s`;
    const minutes = Math.floor(waitTime / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  if (!waitingParticipants) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-96">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Waiting Room</h2>
              <p className="text-sm text-gray-500">
                {waitingParticipants.length} participant{waitingParticipants.length !== 1 ? 's' : ''} waiting
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {waitingParticipants.length === 0 ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No one waiting</h3>
              <p className="text-gray-500">Participants will appear here when they request to join the meeting.</p>
            </div>
          ) : (
            <>
              {/* Bulk Actions */}
              {isHost && waitingParticipants.length > 1 && (
                <div className="p-4 border-b border-gray-200">
                  <button
                    onClick={handleAdmitAll}
                    disabled={isAdmittingAll}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isAdmittingAll ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Admitting all...</span>
                      </>
                    ) : (
                      <>
                        <UserPlus className="w-4 h-4" />
                        <span>Admit All ({waitingParticipants.length})</span>
                      </>
                    )}
                  </button>
                </div>
              )}

              {/* Participants List */}
              <div className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-3">
                  {waitingParticipants.map((participant) => (
                    <div key={participant._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {/* Avatar */}
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                          {participant.userId[0]?.toUpperCase() || 'U'}
                        </div>
                        
                        {/* Info */}
                        <div>
                          <p className="font-medium text-gray-900">{participant.userId}</p>
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>Waiting {formatWaitTime(participant.joinedAt)}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      {isHost && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleAdmitParticipant(participant.userId)}
                            disabled={processingParticipants.has(participant.userId)}
                            className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors disabled:opacity-50"
                            title="Admit to meeting"
                          >
                            {processingParticipants.has(participant.userId) ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                            ) : (
                              <UserCheck className="w-4 h-4" />
                            )}
                          </button>
                          
                          <button
                            onClick={() => handleDenyParticipant(participant.userId)}
                            disabled={processingParticipants.has(participant.userId)}
                            className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors disabled:opacity-50"
                            title="Deny entry"
                          >
                            {processingParticipants.has(participant.userId) ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                            ) : (
                              <UserX className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        {!isHost && (
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <AlertCircle className="w-4 h-4" />
              <span>Only hosts and co-hosts can admit participants</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Waiting Room Notification Component for participants
export function WaitingRoomNotification({ roomName }: { roomName: string }) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <Clock className="w-8 h-8 text-blue-600" />
        </div>
        
        <h1 className="text-2xl font-semibold text-gray-900 mb-4">You're in the waiting room</h1>
        
        <p className="text-gray-600 mb-6">
          The host will let you in soon. Please wait while they review your request to join "{roomName}".
        </p>
        
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <div className="animate-pulse w-2 h-2 bg-blue-600 rounded-full"></div>
          <span>Waiting for host approval...</span>
        </div>
        
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <p className="text-xs text-gray-500">
            Make sure your camera and microphone are ready. You'll be able to join once admitted.
          </p>
        </div>
      </div>
    </div>
  );
}
