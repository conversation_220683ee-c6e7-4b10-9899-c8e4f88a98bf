"use client";

import React from 'react';
import { useParticipants } from '@livekit/components-react';
import { Track } from 'livekit-client';
import { Mic, MicOff, Video, VideoOff, Monitor } from 'lucide-react';

interface VideoLayoutProps {
  tracks: any[]; // Simplified for now
}

// Custom video component for participants
function ParticipantVideo({ participant, className = "" }: { participant: any; className?: string }) {
  return (
    <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
      {participant.isCameraEnabled ? (
        <div className="w-full h-full bg-gray-800 flex items-center justify-center">
          <div className="text-center text-gray-400">
            <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2 text-white text-xl font-semibold">
              {(participant.name || participant.identity)[0]?.toUpperCase() || 'A'}
            </div>
            <p className="text-sm">{participant.name || participant.identity}</p>
            <p className="text-xs opacity-75">Video enabled</p>
          </div>
        </div>
      ) : (
        <div className="w-full h-full bg-gray-800 flex items-center justify-center">
          <div className="text-center text-gray-400">
            <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2 text-white text-xl font-semibold">
              {(participant.name || participant.identity)[0]?.toUpperCase() || 'A'}
            </div>
            <p className="text-sm">{participant.name || participant.identity}</p>
          </div>
        </div>
      )}
      
      {/* Participant Info Overlay */}
      <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-sm">
        <div className="flex items-center space-x-1">
          {participant.isMicrophoneEnabled ? (
            <Mic className="w-3 h-3 text-green-400" />
          ) : (
            <MicOff className="w-3 h-3 text-red-400" />
          )}
          <span className="truncate">
            {participant.name || participant.identity}
            {participant.isLocal && ' (You)'}
          </span>
        </div>
      </div>
      
      {/* Video Status */}
      {!participant.isCameraEnabled && (
        <div className="absolute top-2 right-2">
          <VideoOff className="w-5 h-5 text-red-400" />
        </div>
      )}
    </div>
  );
}

export function SpeakerLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  
  // Find the active speaker or first participant
  const activeSpeaker = participants.find(p => p.isSpeaking) || participants[0];
  const otherParticipants = participants.filter(p => p !== activeSpeaker);

  return (
    <div className="h-full flex flex-col">
      {/* Main Speaker Area */}
      <div className="flex-1 p-2">
        {activeSpeaker ? (
          <ParticipantVideo 
            participant={activeSpeaker} 
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full bg-black rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-400">
              <Video className="w-16 h-16 mx-auto mb-4" />
              <p className="text-lg">No participants</p>
            </div>
          </div>
        )}
      </div>

      {/* Thumbnail Strip */}
      {otherParticipants.length > 0 && (
        <div className="h-24 p-2">
          <div className="flex space-x-2 h-full overflow-x-auto">
            {otherParticipants.map((participant) => (
              <ParticipantVideo
                key={participant.identity}
                participant={participant}
                className="flex-shrink-0 w-32 h-full"
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export function PresentationLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  
  // Find screen share track
  const screenShareTrack = tracks.find(track => 'source' in track && track.source === Track.Source.ScreenShare);
  const presenter = screenShareTrack && 'participant' in screenShareTrack ? screenShareTrack.participant : null;
  const otherParticipants = presenter ? participants.filter(p => p !== presenter) : participants;
  
  if (!screenShareTrack) {
    // Fallback to speaker layout if no screen share
    return <SpeakerLayout tracks={tracks} />;
  }

  return (
    <div className="h-full flex">
      {/* Main Screen Share Area */}
      <div className="flex-1 p-2">
        <div className="h-full bg-black rounded-lg relative overflow-hidden">
          {screenShareTrack && 'participant' in screenShareTrack ? (
            <video
              className="w-full h-full object-contain"
              autoPlay
              playsInline
              ref={(ref) => {
                if (ref && screenShareTrack.publication?.track) {
                  screenShareTrack.publication.track.attach(ref);
                }
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <Monitor className="w-16 h-16 mx-auto mb-4" />
                <p className="text-lg">No screen share available</p>
              </div>
            </div>
          )}
          
          {/* Presenter Info */}
          <div className="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded-lg">
            <div className="flex items-center space-x-2">
              <Monitor className="w-4 h-4" />
              <span className="text-sm font-medium">
                {presenter ? (presenter.name || presenter.identity) + ' is presenting' : 'Presenting'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Participants Sidebar */}
      <div className="w-64 p-2">
        <div className="h-full flex flex-col space-y-2">
          {/* Presenter Video */}
          {presenter && (
            <ParticipantVideo
              participant={presenter}
              className="h-36"
            />
          )}

          {/* Other Participants */}
          <div className="flex-1 overflow-y-auto space-y-2">
            {otherParticipants.map((participant) => (
              <ParticipantVideo
                key={participant.identity}
                participant={participant}
                className="h-24"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Grid Layout with better responsive design
export function EnhancedGridLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  
  const getGridClass = () => {
    const count = participants.length;
    if (count <= 1) return 'grid-cols-1';
    if (count <= 4) return 'grid-cols-2';
    if (count <= 9) return 'grid-cols-3';
    if (count <= 16) return 'grid-cols-4';
    return 'grid-cols-5';
  };

  return (
    <div className={`h-full p-4 grid gap-4 ${getGridClass()}`}>
      {participants.map((participant) => (
        <ParticipantVideo
          key={participant.identity}
          participant={participant}
          className="min-h-0" // Important for grid layout
        />
      ))}
    </div>
  );
}