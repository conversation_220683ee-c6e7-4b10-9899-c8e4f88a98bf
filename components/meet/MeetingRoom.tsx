"use client";

import React, { useState, useEffect } from 'react';
import {
  LiveKitRoom,
  GridLayout,
  ParticipantTile,
  <PERSON><PERSON>,
  RoomAudioRenderer,
  useParticipants,
  useTracks,
  useLocalParticipant,
  useRoomContext,
  TrackReference,
  TrackReferenceOrPlaceholder,
} from '@livekit/components-react';
import { Track, RoomEvent } from 'livekit-client';
import { 
  Clock,
  Users,
  Copy,
  Share2,
  Settings,
  Monitor,
  Grid3X3,
  User,
  Presentation
} from 'lucide-react';
import { ParticipantsList } from './ParticipantsList';
import { SpeakerLayout, PresentationLayout, EnhancedGridLayout } from './VideoLayouts';
import { MeetingControls } from './MeetingControls';
import { WaitingRoomIndicator } from './WaitingRoomIndicator';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface MeetingRoomProps {
  roomName: string;
  userName: string;
  roomId?: string;
}

interface MeetingInfo {
  title: string;
  startTime: Date;
  duration: string;
}

export function MeetingRoom({ roomName, userName, roomId }: MeetingRoomProps) {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isParticipantsOpen, setIsParticipantsOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [layout, setLayout] = useState<'grid' | 'speaker' | 'presentation'>('grid');
  const [meetingInfo, setMeetingInfo] = useState<MeetingInfo>({
    title: `Meeting: ${roomName}`,
    startTime: new Date(),
    duration: '00:00'
  });

  const participants = useParticipants();
  const { localParticipant } = useLocalParticipant();
  const room = useRoomContext();

  // Get room data for waiting room functionality
  const roomData = useQuery(api.rooms.getByName, roomId ? { name: roomId } : "skip");
  
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  // Calculate meeting duration
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const diff = now.getTime() - meetingInfo.startTime.getTime();
      const hours = Math.floor(diff / 3600000);
      const minutes = Math.floor((diff % 3600000) / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);
      
      const duration = hours > 0 
        ? `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        : `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
      setMeetingInfo(prev => ({ ...prev, duration }));
    }, 1000);

    return () => clearInterval(interval);
  }, [meetingInfo.startTime]);

  // Auto-switch to presentation layout when screen sharing starts
  useEffect(() => {
    const hasScreenShare = tracks.some(track => track.source === Track.Source.ScreenShare);
    if (hasScreenShare && layout !== 'presentation') {
      setLayout('presentation');
    }
  }, [tracks, layout]);

  // Get current user role (simplified - in real app would come from database)
  const isHost = localParticipant?.identity === room?.localParticipant?.identity;
  const canModerate = isHost; // Simplified for now

  const copyMeetingLink = () => {
    navigator.clipboard.writeText(window.location.href);
    // TODO: Show toast notification
  };

  const leaveMeeting = () => {
    if (room) {
      room.disconnect();
    }
  };

  // Type guard to filter out placeholders
  const isTrackReference = (trackRef: TrackReferenceOrPlaceholder): trackRef is TrackReference => {
    return trackRef.publication !== undefined;
  };

  const renderVideoLayout = () => {
    // Filter out placeholder tracks using type guard
    const actualTracks = tracks.filter(isTrackReference);
    
    switch (layout) {
      case 'speaker':
        return <SpeakerLayout tracks={actualTracks} />;
      case 'presentation':
        return <PresentationLayout tracks={actualTracks} />;
      case 'grid':
      default:
        return <EnhancedGridLayout tracks={actualTracks} />;
    }
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Professional Meeting Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          {/* Meeting Info */}
          <div className="flex items-center space-x-6">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{meetingInfo.title}</h1>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{meetingInfo.duration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4" />
                  <span>{participants.length} participants</span>
                </div>
                {/* Waiting Room Indicator */}
                {roomData && (
                  <WaitingRoomIndicator
                    roomId={roomData._id}
                    isHost={canModerate}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-4">
            {/* Layout Selector */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setLayout('grid')}
                className={`flex items-center space-x-1 px-3 py-2 text-sm rounded-md transition-colors ${
                  layout === 'grid' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Grid3X3 className="w-4 h-4" />
                <span>Grid</span>
              </button>
              <button
                onClick={() => setLayout('speaker')}
                className={`flex items-center space-x-1 px-3 py-2 text-sm rounded-md transition-colors ${
                  layout === 'speaker' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Speaker view"
              >
                <User className="w-4 h-4" />
                <span>Speaker</span>
              </button>
              <button
                onClick={() => setLayout('presentation')}
                className={`flex items-center space-x-1 px-3 py-2 text-sm rounded-md transition-colors ${
                  layout === 'presentation' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Presentation view"
              >
                <Presentation className="w-4 h-4" />
                <span>Present</span>
              </button>
            </div>

            {/* Meeting Actions */}
            <button
              onClick={copyMeetingLink}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Copy meeting link"
            >
              <Share2 className="w-5 h-5" />
            </button>

            <button
              onClick={() => setIsParticipantsOpen(!isParticipantsOpen)}
              className={`p-2 rounded-lg transition-colors ${
                isParticipantsOpen ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title="Participants"
            >
              <Users className="w-5 h-5" />
            </button>
            
            <button
              onClick={() => setIsChatOpen(!isChatOpen)}
              className={`p-2 rounded-lg transition-colors ${
                isChatOpen ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title="Chat"
            >
              <Monitor className="w-5 h-5" />
            </button>
            
            <button
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Settings"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Video Area */}
        <div className="flex-1 bg-gray-900 relative">
          {renderVideoLayout()}
        </div>

        {/* Enhanced Sidebar */}
        {(isParticipantsOpen || isChatOpen) && (
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col flex-shrink-0">
            {/* Sidebar Tabs */}
            <div className="border-b border-gray-200 p-4">
              <div className="flex space-x-1">
                <button
                  onClick={() => {
                    setIsParticipantsOpen(true);
                    setIsChatOpen(false);
                  }}
                  className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    isParticipantsOpen 
                      ? 'text-blue-600 bg-blue-50' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Participants ({participants.length})
                </button>
                <button
                  onClick={() => {
                    setIsChatOpen(true);
                    setIsParticipantsOpen(false);
                  }}
                  className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    isChatOpen 
                      ? 'text-blue-600 bg-blue-50' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Chat
                </button>
              </div>
            </div>

            {/* Sidebar Content */}
            <div className="flex-1 overflow-hidden">
              {isParticipantsOpen && <ParticipantsList participants={participants} canModerate={canModerate} />}
              {isChatOpen && (
                <div className="h-full">
                  <Chat style={{ height: '100%' }} />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Control Bar */}
      <MeetingControls
        onToggleChat={() => setIsChatOpen(!isChatOpen)}
        onToggleParticipants={() => setIsParticipantsOpen(!isParticipantsOpen)}
        onLeaveMeeting={leaveMeeting}
        canModerate={canModerate}
        participantCount={participants.length}
        isChatOpen={isChatOpen}
        isParticipantsOpen={isParticipantsOpen}
        roomId={roomData?._id}
        roomName={roomName}
      />

      {/* Audio Renderer */}
      <RoomAudioRenderer />
    </div>
  );
}
