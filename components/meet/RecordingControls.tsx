"use client";

import React, { useState, useEffect } from 'react';
import { 
  Circle, 
  Square, 
  Download, 
  Settings, 
  Clock,
  AlertCircle,
  CheckCircle,
  Loader,
  Play,
  Pause
} from 'lucide-react';
import { useAction, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface RecordingControlsProps {
  roomId: Id<"rooms">;
  roomName: string;
  isHost: boolean;
  className?: string;
}

interface RecordingSettings {
  recordAudio: boolean;
  recordVideo: boolean;
  recordScreen: boolean;
}

export function RecordingControls({ roomId, roomName, isHost, className = "" }: RecordingControlsProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingSettings, setRecordingSettings] = useState<RecordingSettings>({
    recordAudio: true,
    recordVideo: true,
    recordScreen: false,
  });
  const [showSettings, setShowSettings] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState<string>('idle');
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [recordingStartTime, setRecordingStartTime] = useState<number | null>(null);
  const [egressId, setEgressId] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);

  // Actions
  const startRecording = useAction(api.livekit.startRecording);
  const stopRecording = useAction(api.livekit.stopRecording);
  const getRecordingStatus = useAction(api.livekit.getRecordingStatus);

  // Get room data to check current recording state
  const roomData = useQuery(api.rooms.get, { id: roomId });

  // Update recording duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRecording && recordingStartTime) {
      interval = setInterval(() => {
        const duration = Math.floor((Date.now() - recordingStartTime) / 1000);
        setRecordingDuration(duration);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, recordingStartTime]);

  // Check recording status periodically
  useEffect(() => {
    let statusInterval: NodeJS.Timeout;
    
    if (egressId && isRecording) {
      statusInterval = setInterval(async () => {
        try {
          const status = await getRecordingStatus({ egressId });
          setRecordingStatus(typeof status.status === 'string' ? status.status : String(status.status || 'unknown'));
          
          if (status.downloadUrl) {
            setDownloadUrl(status.downloadUrl);
          }
          
          // If recording ended, update state  
          const statusStr = typeof status.status === 'string' ? status.status : String(status.status || 'unknown');
          if (statusStr === 'ended' || statusStr === 'failed') {
            setIsRecording(false);
            setRecordingStartTime(null);
          }
        } catch (error) {
          console.error('Failed to get recording status:', error);
        }
      }, 5000); // Check every 5 seconds
    }

    return () => {
      if (statusInterval) clearInterval(statusInterval);
    };
  }, [egressId, isRecording, getRecordingStatus]);

  const handleStartRecording = async () => {
    if (!isHost) return;
    
    try {
      setRecordingStatus('starting');
      const result = await startRecording({
        roomName,
        recordingSettings,
      });
      
      if (result.success) {
        setIsRecording(true);
        setEgressId(result.egressId);
        setRecordingStartTime(Date.now());
        setRecordingStatus('active');
      }
    } catch (error) {
      console.error('Failed to start recording:', error);
      setRecordingStatus('failed');
    }
  };

  const handleStopRecording = async () => {
    if (!isHost || !egressId) return;
    
    try {
      setRecordingStatus('ending');
      const result = await stopRecording({
        roomName,
        egressId,
      });
      
      if (result.success) {
        setIsRecording(false);
        setRecordingStartTime(null);
        setRecordingStatus('ended');
        
        if (result.downloadUrl) {
          setDownloadUrl(result.downloadUrl);
        }
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setRecordingStatus('failed');
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = () => {
    switch (recordingStatus) {
      case 'starting':
      case 'ending':
        return <Loader className="w-4 h-4 animate-spin" />;
      case 'active':
        return <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />;
      case 'ended':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (recordingStatus) {
      case 'starting':
        return 'Starting...';
      case 'active':
        return `Recording ${formatDuration(recordingDuration)}`;
      case 'ending':
        return 'Stopping...';
      case 'ended':
        return 'Recording saved';
      case 'failed':
        return 'Recording failed';
      default:
        return 'Ready to record';
    }
  };

  if (!isHost) {
    // Show recording indicator for non-hosts
    if (isRecording) {
      return (
        <div className="flex items-center space-x-2 px-3 py-2 bg-red-100 text-red-700 rounded-lg">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium">Recording</span>
        </div>
      );
    }
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Main Recording Button */}
      <div className="flex items-center space-x-2">
        <button
          onClick={isRecording ? handleStopRecording : handleStartRecording}
          disabled={recordingStatus === 'starting' || recordingStatus === 'ending'}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            isRecording
              ? 'bg-red-500 text-white hover:bg-red-600'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {isRecording ? (
            <Square className="w-4 h-4" />
          ) : (
            <Circle className="w-4 h-4" />
          )}
          <span className="text-sm">
            {isRecording ? 'Stop' : 'Record'}
          </span>
        </button>

        {/* Recording Settings */}
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          title="Recording settings"
        >
          <Settings className="w-4 h-4" />
        </button>

        {/* Download Button */}
        {downloadUrl && (
          <a
            href={downloadUrl}
            download
            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-colors"
            title="Download recording"
          >
            <Download className="w-4 h-4" />
          </a>
        )}
      </div>

      {/* Recording Status */}
      {(isRecording || recordingStatus !== 'idle') && (
        <div className="flex items-center space-x-2 mt-2 text-sm text-gray-600">
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      )}

      {/* Recording Settings Modal */}
      {showSettings && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-10">
          <h3 className="font-medium text-gray-900 mb-3">Recording Settings</h3>
          
          <div className="space-y-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={recordingSettings.recordAudio}
                onChange={(e) => setRecordingSettings(prev => ({
                  ...prev,
                  recordAudio: e.target.checked
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Record audio</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={recordingSettings.recordVideo}
                onChange={(e) => setRecordingSettings(prev => ({
                  ...prev,
                  recordVideo: e.target.checked
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Record video</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={recordingSettings.recordScreen}
                onChange={(e) => setRecordingSettings(prev => ({
                  ...prev,
                  recordScreen: e.target.checked
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Record screen shares</span>
            </label>
          </div>
          
          <div className="mt-4 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Recordings are saved to cloud storage and can be downloaded after the meeting.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
