"use client";

import React, { useState } from 'react';
import { Users, Clock, UserPlus } from 'lucide-react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { WaitingRoom } from './WaitingRoom';

interface WaitingRoomIndicatorProps {
  roomId: Id<"rooms">;
  isHost: boolean;
}

export function WaitingRoomIndicator({ roomId, isHost }: WaitingRoomIndicatorProps) {
  const [showWaitingRoom, setShowWaitingRoom] = useState(false);
  
  const waitingParticipants = useQuery(api.rooms.getWaitingParticipants, { roomId });
  
  if (!waitingParticipants || waitingParticipants.length === 0) {
    return null;
  }

  return (
    <>
      {/* Indicator Button */}
      <button
        onClick={() => setShowWaitingRoom(true)}
        className="flex items-center space-x-2 px-3 py-2 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors animate-pulse"
        title={`${waitingParticipants.length} participant${waitingParticipants.length !== 1 ? 's' : ''} waiting`}
      >
        <div className="relative">
          <Users className="w-4 h-4" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">{waitingParticipants.length}</span>
          </div>
        </div>
        <span className="text-sm font-medium">
          {waitingParticipants.length} waiting
        </span>
      </button>

      {/* Waiting Room Modal */}
      {showWaitingRoom && (
        <WaitingRoom
          roomId={roomId}
          isHost={isHost}
          onClose={() => setShowWaitingRoom(false)}
        />
      )}
    </>
  );
}

// Compact version for mobile or smaller spaces
export function WaitingRoomBadge({ roomId, isHost }: WaitingRoomIndicatorProps) {
  const [showWaitingRoom, setShowWaitingRoom] = useState(false);
  
  const waitingParticipants = useQuery(api.rooms.getWaitingParticipants, { roomId });
  
  if (!waitingParticipants || waitingParticipants.length === 0) {
    return null;
  }

  return (
    <>
      {/* Compact Badge */}
      <button
        onClick={() => setShowWaitingRoom(true)}
        className="relative p-2 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors"
        title={`${waitingParticipants.length} participant${waitingParticipants.length !== 1 ? 's' : ''} waiting`}
      >
        <Users className="w-5 h-5" />
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-xs text-white font-bold">{waitingParticipants.length}</span>
        </div>
      </button>

      {/* Waiting Room Modal */}
      {showWaitingRoom && (
        <WaitingRoom
          roomId={roomId}
          isHost={isHost}
          onClose={() => setShowWaitingRoom(false)}
        />
      )}
    </>
  );
}
