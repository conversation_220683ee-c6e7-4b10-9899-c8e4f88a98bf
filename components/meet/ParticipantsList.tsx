"use client";

import React, { useState } from 'react';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  MoreVertical,
  Hand,
  Shield,
  Crown,
  UserX,
  VolumeX,
  Volume2,
  Users
} from 'lucide-react';
import { Participant } from 'livekit-client';

interface ParticipantsListProps {
  participants: Participant[];
  canModerate: boolean;
}

interface ParticipantItemProps {
  participant: Participant;
  canModerate: boolean;
  onMute?: (participantId: string) => void;
  onRemove?: (participantId: string) => void;
  onPromote?: (participantId: string) => void;
}

function ParticipantItem({ participant, canModerate, onMute, onRemove, onPromote }: ParticipantItemProps) {
  const [showActions, setShowActions] = useState(false);
  
  const isHost = participant.metadata ? JSON.parse(participant.metadata).role === 'host' : false;
  const isModerator = participant.metadata ? JSON.parse(participant.metadata).role === 'moderator' : false;
  
  return (
    <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg group">
      <div className="flex items-center space-x-3 flex-1">
        {/* Avatar */}
        <div className="relative">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
            {(participant.name || participant.identity)[0]?.toUpperCase() || 'A'}
          </div>
          
          {/* Role Badge */}
          {isHost && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
              <Crown className="w-2.5 h-2.5 text-white" />
            </div>
          )}
          {isModerator && !isHost && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
              <Shield className="w-2.5 h-2.5 text-white" />
            </div>
          )}
        </div>

        {/* Participant Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="font-medium text-gray-900 truncate">
              {participant.name || participant.identity}
              {participant.isLocal && ' (You)'}
            </p>
            {isHost && <span className="text-xs text-yellow-600 font-medium">Host</span>}
            {isModerator && !isHost && <span className="text-xs text-blue-600 font-medium">Mod</span>}
          </div>
          
          {/* Media Status */}
          <div className="flex items-center space-x-2 mt-1">
            <div className="flex items-center space-x-1">
              {participant.isMicrophoneEnabled ? (
                <div className="flex items-center space-x-1">
                  <Mic className="w-3 h-3 text-green-500" />
                  {participant.isSpeaking && (
                    <div className="flex space-x-0.5">
                      <div className="w-1 h-2 bg-green-500 rounded animate-pulse" />
                      <div className="w-1 h-3 bg-green-500 rounded animate-pulse" style={{ animationDelay: '0.1s' }} />
                      <div className="w-1 h-2 bg-green-500 rounded animate-pulse" style={{ animationDelay: '0.2s' }} />
                    </div>
                  )}
                </div>
              ) : (
                <MicOff className="w-3 h-3 text-red-500" />
              )}
            </div>
            
            <div className="w-px h-3 bg-gray-300" />
            
            {participant.isCameraEnabled ? (
              <Video className="w-3 h-3 text-green-500" />
            ) : (
              <VideoOff className="w-3 h-3 text-red-500" />
            )}
            
            {participant.isScreenShareEnabled && (
              <>
                <div className="w-px h-3 bg-gray-300" />
                <span className="text-xs text-blue-600 font-medium">Sharing</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      {canModerate && !participant.isLocal && (
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreVertical className="w-4 h-4" />
          </button>
          
          {showActions && (
            <div className="absolute right-0 top-8 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
              <button
                onClick={() => {
                  onMute?.(participant.identity);
                  setShowActions(false);
                }}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              >
                {participant.isMicrophoneEnabled ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                <span>{participant.isMicrophoneEnabled ? 'Mute' : 'Unmute'}</span>
              </button>
              
              {!isHost && (
                <button
                  onClick={() => {
                    onPromote?.(participant.identity);
                    setShowActions(false);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Shield className="w-4 h-4" />
                  <span>Make Moderator</span>
                </button>
              )}
              
              <div className="border-t border-gray-100 my-1" />
              
              <button
                onClick={() => {
                  onRemove?.(participant.identity);
                  setShowActions(false);
                }}
                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
              >
                <UserX className="w-4 h-4" />
                <span>Remove from meeting</span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export function ParticipantsList({ participants, canModerate }: ParticipantsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredParticipants = participants.filter(participant =>
    (participant.name || participant.identity).toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMuteParticipant = (participantId: string) => {
    // TODO: Implement mute functionality
    console.log('Mute participant:', participantId);
  };

  const handleRemoveParticipant = (participantId: string) => {
    // TODO: Implement remove functionality
    console.log('Remove participant:', participantId);
  };

  const handlePromoteParticipant = (participantId: string) => {
    // TODO: Implement promote functionality
    console.log('Promote participant:', participantId);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Search */}
      <div className="p-4 border-b border-gray-200">
        <input
          type="text"
          placeholder="Search participants..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Participants List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2 space-y-1">
          {filteredParticipants.map((participant) => (
            <ParticipantItem
              key={participant.identity}
              participant={participant}
              canModerate={canModerate}
              onMute={handleMuteParticipant}
              onRemove={handleRemoveParticipant}
              onPromote={handlePromoteParticipant}
            />
          ))}
        </div>
        
        {filteredParticipants.length === 0 && (
          <div className="p-8 text-center text-gray-500">
            <Users className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No participants found</p>
          </div>
        )}
      </div>

      {/* Footer Actions */}
      {canModerate && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex space-x-2">
            <button className="flex-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
              Mute All
            </button>
            <button className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Invite
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
