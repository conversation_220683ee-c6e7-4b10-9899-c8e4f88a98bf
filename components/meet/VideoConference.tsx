"use client";

import React, { useState, useEffect } from 'react';
import {
  LiveKitRoom,
  GridLayout,
  ParticipantTile,
  ControlBar,
  <PERSON><PERSON>,
  RoomAudioRenderer,
  useParticipants,
  useTracks,
  useLocalParticipant,
  useRoomContext,
} from '@livekit/components-react';
import { Track, RoomEvent } from 'livekit-client';
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  PhoneOff,
  MessageSquare,
  Users,
  Monitor,
  MoreVertical,
  Settings,
  Hand,
  Shield,
  Clock,
  Copy,
  Share2
} from 'lucide-react';
import { useLiveKitControls } from '../../hooks/useLiveKitControls';
import { ParticipantsList } from './ParticipantsList';
import { SpeakerLayout, PresentationLayout } from './VideoLayouts';
import { MeetingRoom } from './MeetingRoom';

interface VideoConferenceProps {
  roomName: string;
  token: string;
  userName: string;
}

interface MeetingInfo {
  title: string;
  startTime: Date;
  duration: string;
}

// This component now uses the separate MeetingRoom component

export function VideoConference({ roomName, token, userName }: VideoConferenceProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://streamyard-clonez-1zofz2li.livekit.cloud';

  console.log('VideoConference connecting:', {
    serverUrl,
    roomName,
    userName,
    hasToken: !!token
  });

  return (
    <LiveKitRoom
      video={true}
      audio={true}
      token={token}
      serverUrl={serverUrl}
      data-lk-theme="default"
      style={{ height: '100vh' }}
      onConnected={() => console.log('Connected to meeting:', roomName)}
      onDisconnected={() => console.log('Disconnected from meeting:', roomName)}
      onError={(error) => console.error('Meeting error:', error)}
    >
      <MeetingRoom roomName={roomName} userName={userName} roomId={roomName} />
    </LiveKitRoom>
  );
}