 streamyard-clonez/
  ├── 📄 LIVEKIT_INTEGRATION.md
  ├── 📄 streaming_platform_architecture.md
  ├── 📄 system.md
  ├── 📄 package.json
  ├── 📄 package-lock.json
  ├── 📄 next.config.js
  ├── 📄 next-env.d.ts
  ├── 📄 tsconfig.json
  ├── 📄 tsconfig.tsbuildinfo
  ├── 📄 tailwind.config.js
  ├── 📄 postcss.config.js
  ├── 📄 middleware.ts
  ├── 📗 node_modules/
  │
  ├── 📁 app/ (Next.js App Router)
  │   ├── 📄 layout.tsx
  │   ├── 📄 page.tsx
  │   ├── 📄 globals.css
  │   ├── 📁 (auth)/
  │   │   ├── 📁 sign-in/
  │   │   │   └── 📁 [[...sign-in]]/
  │   │   │       └── 📄 page.tsx
  │   │   └── 📁 sign-up/
  │   │       └── 📁 [[...sign-up]]/
  │   │           └── 📄 page.tsx
  │   ├── 📁 (dashboard)/
  │   │   └── 📁 stream/
  │   │       └── 📁 [streamId]/
  │   │           └── 📄 page.tsx
  │   └── 📁 rooms/
  │       └── 📁 [roomName]/
  │           └── 📄 page.tsx
  │
  ├── 📁 components/
  │   ├── 📄 convex-provider.tsx
  │   ├── 📄 header.tsx
  │   ├── 📄 livepeer-provider.tsx
  │   ├── 📄 theme-provider.tsx
  │   ├── 📄 video-player.tsx
  │   ├── 📁 ui/
  │   │   └── 📄 sonner.tsx
  │   ├── 📁 meet/
  │   │   ├── 📄 PreJoinScreen.tsx
  │   │   └── 📄 VideoConference.tsx
  │   ├── 📁 moderation/
  │   │   ├── 📄 BreakoutRoomManager.tsx
  │   │   ├── 📄 ModerationPanel.tsx
  │   │   └── 📄 PermissionControl.tsx
  │   └── 📁 studio/
  │       ├── 📄 BrandingPanel.tsx
  │       ├── 📄 CustomLayoutBuilder.tsx
  │       ├── 📄 GuestManager.tsx
  │       ├── 📄 LayoutManager.tsx
  │       ├── 📄 LiveKitControlPanel.tsx
  │       ├── 📄 LiveKitStudioWrapper.tsx
  │       ├── 📄 StreamChat.tsx
  │       ├── 📄 StreamDestinations.tsx
  │       ├── 📄 StreamMonitor.tsx
  │       ├── 📄 StreamStudio.tsx
  │       └── 📄 StudioVideoDisplay.tsx
  │
  ├── 📁 convex/ (Backend - Convex Database)
  │   ├── 📄 README.md
  │   ├── 📄 tsconfig.json
  │   ├── 📄 auth.config.js
  │   ├── 📄 schema.ts
  │   ├── 📄 streams.ts
  │   ├── 📄 participants.ts
  │   ├── 📄 rooms.ts
  │   ├── 📄 users.ts
  │   ├── 📄 moderation.ts
  │   ├── 📄 livekit.ts
  │   ├── 📁 lib/ (empty)
  │   └── 📁 _generated/
  │       ├── 📄 api.d.ts
  │       ├── 📄 api.js
  │       ├── 📄 dataModel.d.ts
  │       ├── 📄 server.d.ts
  │       └── 📄 server.js
  │
  ├── 📁 hooks/
  │   └── 📄 useLiveKitControls.ts
  │
  └── 📁 lib/
      └── 📄 utils.ts