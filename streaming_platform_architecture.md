# Live Streaming Platform Architecture

## Table of Contents
1. [Core Technologies](#core-technologies)
2. [System Architecture](#system-architecture)
3. [Role-Based Access Control](#role-based-access-control)
4. [Moderation System](#moderation-system)
5. [Breakout Rooms](#breakout-rooms)
6. [API Endpoints](#api-endpoints)
7. [Frontend Components](#frontend-components)
8. [Deployment](#deployment)

## Core Technologies <a name="core-technologies"></a>

### Backend Stack
- **Convex**: Real-time backend with:
  - Database
  - Authentication
  - Serverless functions
- **LiveKit**: WebRTC media server handling:
  - Real-time video/audio
  - SFU architecture
  - Room management
- **Livepeer**: Blockchain-based:
  - Video transcoding
  - Content delivery
  - Stream recording

### Frontend Stack
- Next.js 14 (App Router)
- React 18
- Tailwind CSS
- LiveKit Client SDK
- Convex React Client

## System Architecture <a name="system-architecture"></a>

```mermaid
graph TD
    A[Frontend] --> B[Convex Backend]
    B --> C[LiveKit]
    B --> D[Livepeer]
    C --> E[RTMP Output]
    E --> D
    D --> F[CDN Delivery]
```

### Data Flow
1. Client connects to Convex for application state
2. Media streams connect through LiveKit
3. LiveKit composites streams and outputs via RTMP
4. Livepeer receives RTMP and handles distribution

## Role-Based Access Control <a name="role-based-access-control"></a>

### Global Roles
| Role | Permissions | Overrides |
|------|------------|-----------|
| Master | Full platform access | Can modify any stream |
| Admin | Manage users/content | Cannot modify master roles |
| User | Create/join streams | Limited to owned content |

### Stream Roles
| Role | Capabilities | Moderation Powers |
|------|-------------|-------------------|
| Host | Full control | All actions |
| Co-Host | Most controls | Cannot delete stream |
| Moderator | Basic moderation | Mute/timeout/kick |
| Guest | Limited interaction | None |
| Viewer | Watch only | None |

## Moderation System <a name="moderation-system"></a>

### Available Actions
1. **Mute/Unmute**
   - Duration options: 1min, 5min, 10min, permanent
   - Applies to audio tracks only

2. **Video Disable**
   - Turns off participant camera
   - Can be toggled remotely

3. **Timeout**
   - Temporary ban (1min-24hrs)
   - Automatic reconnection after duration

4. **Kick/Ban**
   - Immediate removal
   - Ban prevents future joins

### Moderation Logs
```typescript
interface ModerationLog {
  timestamp: number;
  moderatorId: string;
  targetUserId: string;
  action: "mute"|"timeout"|"kick"|"ban";
  reason?: string;
  duration?: number;
  streamId: string;
}
```

## Breakout Rooms <a name="breakout-rooms"></a>

### Creation Flow
1. Host specifies:
   - Room name
   - Participant limit (2-50)
   - Auto-return settings
2. System:
   - Creates new LiveKit room
   - Generates access tokens
   - Updates participant records

### Management API
```typescript
// Create room
POST /api/breakout/create

// Move participants
POST /api/breakout/move

// Close room
POST /api/breakout/close
```

## API Endpoints <a name="api-endpoints"></a>

### Stream Management
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/streams` | POST | Create new stream |
| `/api/streams/[id]` | PATCH | Update stream settings |
| `/api/streams/[id]/start` | POST | Begin broadcasting |
| `/api/streams/[id]/end` | POST | End stream |

### Moderation Endpoints
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/moderation/mute` | POST | Mute participant |
| `/api/moderation/timeout` | POST | Timeout participant |
| `/api/moderation/kick` | POST | Remove participant |

## Frontend Components <a name="frontend-components"></a>

### Core UI Elements
1. **Stream Studio**
   - Video grid
   - Participant list
   - Controls overlay

2. **Moderation Panel**
   - Participant cards
   - Quick action buttons
   - Log viewer

3. **Breakout Manager**
   - Room list
   - Participant assignment
   - Status indicators

### Component Hierarchy
```mermaid
graph TD
    A[App] --> B[StreamLayout]
    B --> C[VideoGrid]
    B --> D[ControlPanel]
    D --> E[ModerationTools]
    D --> F[BreakoutManager]
```

## Deployment <a name="deployment"></a>

### Infrastructure Requirements
- **Convex**: Managed service
- **LiveKit**: Self-hosted or cloud
- **Livepeer**: Managed service
- **Frontend**: Vercel/Netlify

### Environment Variables
```env
CONVEX_DEPLOYMENT=production
LIVEKIT_API_KEY=your_key
LIVEKIT_API_SECRET=your_secret
LIVEPEER_API_KEY=your_key
```

### CI/CD Pipeline
1. Code commit triggers build
2. Tests run (unit + integration)
3. Infrastructure provisioned
4. Frontend deployed
5. Backend updated





1. Package Setup
{
  "name": "streamyard-clone",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "npm-run-all --parallel dev:backend dev:frontend",
    "build": "tsc && next build 2>&1",
    "dev:backend": "convex dev",
    "dev:frontend": "next dev",
    "predev": "convex dev --until-success",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@clerk/nextjs": "^6.24.0",
    "@livekit/components-react": "^2.0.0",
    "@livekit/components-styles": "^1.0.0",
    "@livepeer/react": "^4.3.6",
    "clsx": "^2.1.1",
    "convex": "^1.25.2",
    "livekit-client": "^2.0.0",
    "livekit-server-sdk": "^2.0.0",
    "lucide-react": "^0.525.0",
    "next": "^15.3.5",
    "next-themes": "^0.4.6",
    "npm-run-all2": "^8.0.4",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "sonner": "^2.0.6",
    "tailwind-merge": "^3.3.1",
    "tailwindcss": "^3.3.0",
    "zod": "^3.22.0"
  },
  "devDependencies": {
    "@types/node": "20.19.6",
    "@types/react": "18.3.23",
    "@types/react-dom": "^18.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "14.0.0",
    "typescript": "5.8.3"
  }
}


2. Convex Schema
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  streams: defineTable({
    hostId: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("live"),
      v.literal("ended")
    ),
    // LiveKit integration
    livekitRoomName: v.string(),
    livekitToken: v.optional(v.string()),
    // Livepeer integration
    livepeerStreamId: v.optional(v.string()),
    livepeerStreamKey: v.optional(v.string()),
    livepeerPlaybackId: v.optional(v.string()),
    // Egress tracking
    egressId: v.optional(v.string()),
    // Timestamps
    createdAt: v.number(),
    startedAt: v.optional(v.number()),
    endedAt: v.optional(v.number()),
  })
    .index("by_hostId", ["hostId"])
    .index("by_status", ["status"]),

  participants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    name: v.string(),
    email: v.optional(v.string()),
    role: v.union(v.literal("host"), v.literal("guest")),
    status: v.union(
      v.literal("invited"),
      v.literal("joined"),
      v.literal("on_stage"),
      v.literal("backstage"),
      v.literal("left")
    ),
    joinedAt: v.optional(v.number()),
    leftAt: v.optional(v.number()),
  })
    .index("by_streamId", ["streamId"])
    .index("by_userId", ["userId"]),

  chatMessages: defineTable({
    streamId: v.id("streams"),
    participantId: v.id("participants"),
    authorName: v.string(),
    message: v.string(),
    timestamp: v.number(),
    type: v.union(v.literal("message"), v.literal("system")),
  }).index("by_streamId", ["streamId"]),

  invitations: defineTable({
    streamId: v.id("streams"),
    email: v.string(),
    token: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("expired")
    ),
    createdAt: v.number(),
    expiresAt: v.number(),
  })
    .index("by_streamId", ["streamId"])
    .index("by_token", ["token"]),
});
export default schema;

3. Convex Utilities
// convex/lib/livekit.ts
import { AccessToken } from "livekit-server-sdk";

export interface LiveKitConfig {
  host: string;
  apiKey: string;
  apiSecret: string;
}

export class LiveKitService {
  private config: LiveKitConfig;

  constructor(config: LiveKitConfig) {
    this.config = config;
  }

  generateToken(roomName: string, identity: string, name?: string): string {
    const token = new AccessToken(
      this.config.apiKey,
      this.config.apiSecret,
      {
        identity,
        name,
      }
    );

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    return token.toJwt();
  }

  async createRoom(roomName: string): Promise<void> {
    const token = new AccessToken(
      this.config.apiKey,
      this.config.apiSecret,
      { identity: "api-service" }
    );

    const response = await fetch(
      `https://${this.config.host}/twirp/livekit.RoomService/CreateRoom`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token.toJwt()}`,
        },
        body: JSON.stringify({
          name: roomName,
          empty_timeout: 300, // 5 minutes
          max_participants: 50,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to create LiveKit room: ${response.statusText}`);
    }
  }

  async startEgress(
    roomName: string,
    rtmpUrl: string,
    streamKey: string
  ): Promise<string> {
    const token = new AccessToken(
      this.config.apiKey,
      this.config.apiSecret,
      { identity: "api-service" }
    );

    const response = await fetch(
      `https://${this.config.host}/twirp/livekit.Egress/StartRoomCompositeEgress`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token.toJwt()}`,
        },
        body: JSON.stringify({
          room_name: roomName,
          layout: "grid",
          audio_only: false,
          video_only: false,
          custom_base_url: "",
          output: {
            case: "stream",
            value: {
              protocol: "RTMP",
              urls: [`${rtmpUrl}/${streamKey}`],
            },
          },
        }),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to start egress: ${error}`);
    }

    const result = await response.json();
    return result.egress_id;
  }

  async stopEgress(egressId: string): Promise<void> {
    const token = new AccessToken(
      this.config.apiKey,
      this.config.apiSecret,
      { identity: "api-service" }
    );

    const response = await fetch(
      `https://${this.config.host}/twirp/livekit.Egress/StopEgress`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token.toJwt()}`,
        },
        body: JSON.stringify({
          egress_id: egressId,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to stop egress: ${response.statusText}`);
    }
  }
}

// convex/lib/livepeer.ts
export interface LivepeerConfig {
  apiKey: string;
  baseUrl: string;
}

export interface LivepeerStream {
  id: string;
  streamKey: string;
  playbackId: string;
  rtmpIngestUrl: string;
}

export class LivepeerService {
  private config: LivepeerConfig;

  constructor(config: LivepeerConfig) {
    this.config = config;
  }

  async createStream(name: string): Promise<LivepeerStream> {
    const response = await fetch(`${this.config.baseUrl}/stream`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.config.apiKey}`,
      },
      body: JSON.stringify({
        name,
        profiles: [
          {
            name: "720p",
            bitrate: 2000000,
            fps: 30,
            width: 1280,
            height: 720,
          },
          {
            name: "480p",
            bitrate: 1000000,
            fps: 30,
            width: 854,
            height: 480,
          },
          {
            name: "360p",
            bitrate: 500000,
            fps: 30,
            width: 640,
            height: 360,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create Livepeer stream: ${response.statusText}`);
    }

    const stream = await response.json();
    return {
      id: stream.id,
      streamKey: stream.streamKey,
      playbackId: stream.playbackId,
      rtmpIngestUrl: `rtmp://rtmp.livepeer.com/live`,
    };
  }

  async deleteStream(streamId: string): Promise<void> {
    const response = await fetch(
      `${this.config.baseUrl}/stream/${streamId}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to delete Livepeer stream: ${response.statusText}`);
    }
  }
}


4. Convex Functions


5. Convex Schema

convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // User roles and permissions
  users: defineTable({
    userId: v.string(), // Auth provider ID
    email: v.string(),
    name: v.string(),
    globalRole: v.union(
      v.literal("master"),
      v.literal("admin"), 
      v.literal("user")
    ),
    isActive: v.boolean(),
    createdAt: v.number(),
    lastLoginAt: v.optional(v.number()),
  }).index("by_userId", ["userId"]),

  streams: defineTable({
    hostId: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("live"),
      v.literal("ended"),
      v.literal("paused")
    ),
    // LiveKit integration
    livekitRoomName: v.string(),
    // Livepeer integration
    livepeerStreamId: v.optional(v.string()),
    livepeerStreamKey: v.optional(v.string()),
    livepeerPlaybackId: v.optional(v.string()),
    egressId: v.optional(v.string()),
    // Stream settings
    settings: v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireApprovalToJoin: v.boolean(),
      maxParticipants: v.number(),
      recordSession: v.boolean(),
    }),
    // Timestamps
    createdAt: v.number(),
    startedAt: v.optional(v.number()),
    endedAt: v.optional(v.number()),
  })
    .index("by_hostId", ["hostId"])
    .index("by_status", ["status"]),

  participants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    name: v.string(),
    email: v.optional(v.string()),
    role: v.union(
      v.literal("host"),
      v.literal("co_host"), 
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
    status: v.union(
      v.literal("invited"),
      v.literal("pending_approval"),
      v.literal("approved"),
      v.literal("joined"),
      v.literal("on_stage"),
      v.literal("backstage"),
      v.literal("breakout_room"),
      v.literal("left"),
      v.literal("kicked"),
      v.literal("banned")
    ),
    // Permissions
    permissions: v.object({
      canSpeak: v.boolean(),
      canVideo: v.boolean(),
      canScreenShare: v.boolean(),
      canChat: v.boolean(),
      canInviteOthers: v.boolean(),
      canModerate: v.boolean(),
    }),
    // Moderation tracking
    moderationHistory: v.array(v.object({
      action: v.union(
        v.literal("muted"),
        v.literal("unmuted"),
        v.literal("video_disabled"),
        v.literal("video_enabled"),
        v.literal("screen_share_blocked"),
        v.literal("timeout"),
        v.literal("kicked"),
        v.literal("banned"),
        v.literal("moved_to_breakout"),
        v.literal("returned_from_breakout")
      ),
      moderatorId: v.string(),
      reason: v.optional(v.string()),
      timestamp: v.number(),
      duration: v.optional(v.number()), // For timeouts
    })),
    // Current restrictions
    restrictions: v.object({
      isMuted: v.boolean(),
      isVideoDisabled: v.boolean(),
      isScreenShareBlocked: v.boolean(),
      timeoutUntil: v.optional(v.number()),
      breakoutRoomId: v.optional(v.id("breakoutRooms")),
    }),
    joinedAt: v.optional(v.number()),
    leftAt: v.optional(v.number()),
  })
    .index("by_streamId", ["streamId"])
    .index("by_userId", ["userId"])
    .index("by_status", ["status"]),

  // Breakout rooms
  breakoutRooms: defineTable({
    streamId: v.id("streams"),
    name: v.string(),
    livekitRoomName: v.string(),
    createdBy: v.string(),
    status: v.union(
      v.literal("active"),
      v.literal("closed")
    ),
    settings: v.object({
      maxParticipants: v.number(),
      autoReturn: v.boolean(),
      autoReturnAfter: v.optional(v.number()), // minutes
    }),
    createdAt: v.number(),
    closedAt: v.optional(v.number()),
  }).index("by_streamId", ["streamId"]),

  chatMessages: defineTable({
    streamId: v.id("streams"),
    participantId: v.id("participants"),
    authorName: v.string(),
    message: v.string(),
    timestamp: v.number(),
    type: v.union(
      v.literal("message"),
      v.literal("system"),
      v.literal("moderation")
    ),
    isDeleted: v.boolean(),
    deletedBy: v.optional(v.string()),
    breakoutRoomId: v.optional(v.id("breakoutRooms")),
  }).index("by_streamId", ["streamId"]),

  // Moderation logs
  moderationLogs: defineTable({
    streamId: v.id("streams"),
    moderatorId: v.string(),
    targetUserId: v.string(),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("disable_video"),
      v.literal("enable_video"),
      v.literal("block_screen_share"),
      v.literal("allow_screen_share"),
      v.literal("timeout"),
      v.literal("kick"),
      v.literal("ban"),
      v.literal("create_breakout"),
      v.literal("move_to_breakout"),
      v.literal("return_from_breakout"),
      v.literal("delete_message")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
    metadata: v.optional(v.object({
      breakoutRoomId: v.optional(v.id("breakoutRooms")),
      messageId: v.optional(v.id("chatMessages")),
    })),
    timestamp: v.number(),
  }).index("by_streamId", ["streamId"]),

  invitations: defineTable({
    streamId: v.id("streams"),
    email: v.string(),
    role: v.union(
      v.literal("co_host"),
      v.literal("moderator"), 
      v.literal("guest")
    ),
    token: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("expired")
    ),
    invitedBy: v.string(),
    createdAt: v.number(),
    expiresAt: v.number(),
  })
    .index("by_streamId", ["streamId"])
    .index("by_token", ["token"]),
});

export const startBroadcast = action({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const stream = await ctx.runQuery(internal.streams.getById, {
      id: args.streamId,
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    if (stream.status !== "pending") {
      throw new Error("Stream is not in pending state");
    }

    try {
      // Start LiveKit egress to Livepeer
      const egressId = await livekitService.startEgress(
        stream.livekitRoomName,
        "rtmp://rtmp.livepeer.com/live",
        stream.livepeerStreamKey!
      );

      // Update stream status
      await ctx.runMutation(internal.streams.updateStatus, {
        streamId: args.streamId,
        status: "live",
        egressId,
        startedAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to start broadcast:", error);
      throw new Error("Failed to start broadcast");
    }
  },
});

export const stopBroadcast = action({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const stream = await ctx.runQuery(internal.streams.getById, {
      id: args.streamId,
    });

    if (!stream || !stream.egressId) {
      throw new Error("Stream not found or not broadcasting");
    }

    try {
      // Stop LiveKit egress
      await livekitService.stopEgress(stream.egressId);

      // Update stream status
      await ctx.runMutation(internal.streams.updateStatus, {
        streamId: args.streamId,
        status: "ended",
        endedAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to stop broadcast:", error);
      throw new Error("Failed to stop broadcast");
    }
  },
});

export const generateToken = action({
  args: {
    streamId: v.id("streams"),
    userId: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const stream = await ctx.runQuery(internal.streams.getById, {
      id: args.streamId,
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    const token = livekitService.generateToken(
      stream.livekitRoomName,
      args.userId,
      args.name
    );

    return { token };
  },
});

// Internal mutations and queries
export const create = internalAction({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    livekitRoomName: v.string(),
    livepeerStreamId: v.string(),
    livepeerStreamKey: v.string(),
    livepeerPlaybackId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.runMutation(internal.streams.createInternal, args);
  },
});

export const createInternal = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    livekitRoomName: v.string(),
    livepeerStreamId: v.string(),
    livepeerStreamKey: v.string(),
    livepeerPlaybackId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("streams", {
      ...args,
      status: "pending",
      createdAt: Date.now(),
    });
  },
});

export const getById = query({
  args: { id: v.id("streams") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getByHostId = query({
  args: { hostId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("streams")
      .withIndex("by_hostId", (q) => q.eq("hostId", args.hostId))
      .order("desc")
      .collect();
  },
});

export const updateStatus = mutation({
  args: {
    streamId: v.id("streams"),
    status: v.union(v.literal("pending"), v.literal("live"), v.literal("ended")),
    egressId: v.optional(v.string()),
    startedAt: v.optional(v.number()),
    endedAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { streamId, ...updates } = args;
    await ctx.db.patch(streamId, updates);
  },
});


5. TypeScript Types

// src/types/index.ts
import { Id } from "../../convex/_generated/dataModel";

export interface Stream {
  _id: Id<"streams">;
  hostId: string;
  title: string;
  description?: string;
  status: "pending" | "live" | "ended";
  livekitRoomName: string;
  livepeerStreamId?: string;
  livepeerPlaybackId?: string;
  egressId?: string;
  createdAt: number;
  startedAt?: number;
  endedAt?: number;
}

export interface Participant {
  _id: Id<"participants">;
  streamId: Id<"streams">;
  userId: string;
  name: string;
  email?: string;
  role: "host" | "guest";
  status: "invited" | "joined" | "on_stage" | "backstage" | "left";
  joinedAt?: number;
  leftAt?: number;
}

export interface ChatMessage {
  _id: Id<"chatMessages">;
  streamId: Id<"streams">;
  participantId: Id<"participants">;
  authorName: string;
  message: string;
  timestamp: number;
  type: "message" | "system";
}

export interface StreamStudioProps {
  streamId: Id<"streams">;
  userId: string;
  userName: string;
}

6. React Hooks


// src/hooks/useStream.ts
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

export function useStream(streamId: Id<"streams">) {
  const stream = useQuery(api.streams.getById, { id: streamId });
  const participants = useQuery(api.participants.getByStreamId, { streamId });
  const chatMessages = useQuery(api.chat.getByStreamId, { streamId });

  const startBroadcast = useMutation(api.streams.startBroadcast);
  const stopBroadcast = useMutation(api.streams.stopBroadcast);
  const sendMessage = useMutation(api.chat.sendMessage);

  return {
    stream,
    participants,
    chatMessages,
    actions: {
      startBroadcast: () => startBroadcast({ streamId }),
      stopBroadcast: () => stopBroadcast({ streamId }),
      sendMessage: (message: string, participantId: Id<"participants">) =>
        sendMessage({ streamId, participantId, message }),
    },
  };
}

// src/hooks/useLiveKit.ts
import { useEffect, useState } from "react";
import { Room, RoomEvent, RemoteParticipant } from "livekit-client";

export function useLiveKit(token: string, serverUrl: string) {
  const [room, setRoom] = useState<Room | null>(null);
  const [participants, setParticipants] = useState<RemoteParticipant[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const roomInstance = new Room();

    roomInstance.on(RoomEvent.Connected, () => {
      setIsConnected(true);
    });

    roomInstance.on(RoomEvent.Disconnected, () => {
      setIsConnected(false);
    });

    roomInstance.on(RoomEvent.ParticipantConnected, (participant) => {
      setParticipants((prev) => [...prev, participant]);
    });

    roomInstance.on(RoomEvent.ParticipantDisconnected, (participant) => {
      setParticipants((prev) =>
        prev.filter((p) => p.identity !== participant.identity)
      );
    });

    roomInstance.connect(serverUrl, token);
    setRoom(roomInstance);

    return () => {
      roomInstance.disconnect();
    };
  }, [token, serverUrl]);

  return {
    room,
    participants,
    isConnected,
  };
}

Role & Permission System

// convex/lib/permissions.ts
import { Id } from "../_generated/dataModel";

export type GlobalRole = "master" | "admin" | "user";
export type StreamRole = "host" | "co_host" | "moderator" | "guest" | "viewer";

export interface RolePermissions {
  // Stream management
  canCreateStream: boolean;
  canDeleteStream: boolean;
  canModifyStreamSettings: boolean;
  
  // Participant management
  canInviteParticipants: boolean;
  canKickParticipants: boolean;
  canBanParticipants: boolean;
  canTimeoutParticipants: boolean;
  
  // Moderation
  canMuteParticipants: boolean;
  canDisableVideo: boolean;
  canBlockScreenShare: boolean;
  canDeleteMessages: boolean;
  
  // Breakout rooms
  canCreateBreakoutRooms: boolean;
  canMoveToBreakoutRooms: boolean;
  canCloseBreakoutRooms: boolean;
  
  // Advanced
  canViewModerationLogs: boolean;
  canManageRoles: boolean;
  canOverrideMaster: boolean; // Only for master role
}

export const ROLE_PERMISSIONS: Record<GlobalRole, RolePermissions> = {
  master: {
    canCreateStream: true,
    canDeleteStream: true,
    canModifyStreamSettings: true,
    canInviteParticipants: true,
    canKickParticipants: true,
    canBanParticipants: true,
    canTimeoutParticipants: true,
    canMuteParticipants: true,
    canDisableVideo: true,
    canBlockScreenShare: true,
    canDeleteMessages: true,
    canCreateBreakoutRooms: true,
    canMoveToBreakoutRooms: true,
    canCloseBreakoutRooms: true,
    canViewModerationLogs: true,
    canManageRoles: true,
    canOverrideMaster: true,
  },
  admin: {
    canCreateStream: true,
    canDeleteStream: false, // Can't delete others' streams
    canModifyStreamSettings: true,
    canInviteParticipants: true,
    canKickParticipants: true,
    canBanParticipants: true,
    canTimeoutParticipants: true,
    canMuteParticipants: true,
    canDisableVideo: true,
    canBlockScreenShare: true,
    canDeleteMessages: true,
    canCreateBreakoutRooms: true,
    canMoveToBreakoutRooms: true,
    canCloseBreakoutRooms: true,
    canViewModerationLogs: true,
    canManageRoles: false, // Can't change roles
    canOverrideMaster: false,
  },
  user: {
    canCreateStream: true,
    canDeleteStream: false,
    canModifyStreamSettings: false,
    canInviteParticipants: false,
    canKickParticipants: false,
    canBanParticipants: false,
    canTimeoutParticipants: false,
    canMuteParticipants: false,
    canDisableVideo: false,
    canBlockScreenShare: false,
    canDeleteMessages: false,
    canCreateBreakoutRooms: false,
    canMoveToBreakoutRooms: false,
    canCloseBreakoutRooms: false,
    canViewModerationLogs: false,
    canManageRoles: false,
    canOverrideMaster: false,
  },
};

export const STREAM_ROLE_PERMISSIONS: Record<StreamRole, RolePermissions> = {
  host: ROLE_PERMISSIONS.master, // Host has full control in their stream
  co_host: {
    ...ROLE_PERMISSIONS.admin,
    canDeleteStream: false,
    canManageRoles: true, // Co-host can manage roles within the stream
  },
  moderator: {
    ...ROLE_PERMISSIONS.user,
    canKickParticipants: true,
    canTimeoutParticipants: true,
    canMuteParticipants: true,
    canDisableVideo: true,
    canBlockScreenShare: true,
    canDeleteMessages: true,
    canMoveToBreakoutRooms: true,
  },
  guest: ROLE_PERMISSIONS.user,
  viewer: {
    ...ROLE_PERMISSIONS.user,
    canCreateStream: false,
  },
};

export function hasPermission(
  globalRole: GlobalRole,
  streamRole: StreamRole,
  permission: keyof RolePermissions,
  isStreamOwner: boolean = false
): boolean {
  // Master role overrides everything
  if (globalRole === "master") {
    return ROLE_PERMISSIONS.master[permission];
  }
  
  // Stream owner (host) has full control in their stream
  if (isStreamOwner) {
    return ROLE_PERMISSIONS.master[permission];
  }
  
  // Check stream role permissions first, then global role
  const streamPermissions = STREAM_ROLE_PERMISSIONS[streamRole];
  const globalPermissions = ROLE_PERMISSIONS[globalRole];
  
  return streamPermissions[permission] || globalPermissions[permission];
}


Moderation Functions

// convex/moderation.ts
import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { internal } from "./_generated/api";
import { hasPermission } from "./lib/permissions";

export const muteParticipant = action({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    moderatorId: v.string(),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()), // minutes, undefined = permanent
  },
  handler: async (ctx, args) => {
    // Check permissions
    const moderator = await ctx.runQuery(internal.users.getByUserId, {
      userId: args.moderatorId,
    });
    
    const participant = await ctx.runQuery(internal.participants.getByUserIdAndStream, {
      userId: args.targetUserId,
      streamId: args.streamId,
    });

    if (!moderator || !participant) {
      throw new Error("Moderator or participant not found");
    }

    const canMute = hasPermission(
      moderator.globalRole,
      participant.role,
      "canMuteParticipants"
    );

    if (!canMute) {
      throw new Error("Insufficient permissions to mute participant");
    }

    // Apply mute
    await ctx.runMutation(internal.moderation.applyMute, {
      participantId: participant._id,
      moderatorId: args.moderatorId,
      reason: args.reason,
      duration: args.duration,
    });

    // Send LiveKit command to actually mute the participant
    await ctx.runAction(internal.livekit.muteParticipant, {
      roomName: (await ctx.runQuery(internal.streams.getById, { id: args.streamId }))!.livekitRoomName,
      participantIdentity: args.targetUserId,
    });

    return { success: true };
  },
});

export const timeoutParticipant = action({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    moderatorId: v.string(),
    duration: v.number(), // minutes
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await ctx.runQuery(internal.users.getByUserId, {
      userId: args.moderatorId,
    });
    
    const participant = await ctx.runQuery(internal.participants.getByUserIdAndStream, {
      userId: args.targetUserId,
      streamId: args.streamId,
    });

    if (!moderator || !participant) {
      throw new Error("Moderator or participant not found");
    }

    const canTimeout = hasPermission(
      moderator.globalRole,
      participant.role,
      "canTimeoutParticipants"
    );

    if (!canTimeout) {
      throw new Error("Insufficient permissions to timeout participant");
    }

    const timeoutUntil = Date.now() + (args.duration * 60 * 1000);

    await ctx.runMutation(internal.moderation.applyTimeout, {
      participantId: participant._id,
      moderatorId: args.moderatorId,
      timeoutUntil,
      reason: args.reason,
      duration: args.duration,
    });

    // Disconnect participant from LiveKit
    await ctx.runAction(internal.livekit.disconnectParticipant, {
      roomName: (await ctx.runQuery(internal.streams.getById, { id: args.streamId }))!.livekitRoomName,
      participantIdentity: args.targetUserId,
    });

    return { success: true, timeoutUntil };
  },
});

export const createBreakoutRoom = action({
  args: {
    streamId: v.id("streams"),
    name: v.string(),
    createdBy: v.string(),
    maxParticipants: v.number(),
    autoReturn: v.boolean(),
    autoReturnAfter: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const creator = await ctx.runQuery(internal.users.getByUserId, {
      userId: args.createdBy,
    });

    if (!creator) {
      throw new Error("Creator not found");
    }

    const canCreate = hasPermission(
      creator.globalRole,
      "host", // Assume host role for stream context
      "canCreateBreakoutRooms"
    );

    if (!canCreate) {
      throw new Error("Insufficient permissions to create breakout room");
    }

    const roomName = `breakout_${args.streamId}_${Date.now()}`;

    // Create LiveKit room for breakout
    await ctx.runAction(internal.livekit.createRoom, {
      roomName,
    });

    const breakoutRoomId = await ctx.runMutation(internal.breakoutRooms.create, {
      streamId: args.streamId,
      name: args.name,
      livekitRoomName: roomName,
      createdBy: args.createdBy,
      maxParticipants: args.maxParticipants,
      autoReturn: args.autoReturn,
      autoReturnAfter: args.autoReturnAfter,
    });

    return { breakoutRoomId, roomName };
  },
});

export const moveToBreakoutRoom = action({
  args: {
    streamId: v.id("streams"),
    participantIds: v.array(v.id("participants")),
    breakoutRoomId: v.id("breakoutRooms"),
    moderatorId: v.string(),
  },
  handler: async (ctx, args) => {
    const moderator = await ctx.runQuery(internal.users.getByUserId, {
      userId: args.moderatorId,
    });

    if (!moderator) {
      throw new Error("Moderator not found");
    }

    const canMove = hasPermission(
      moderator.globalRole,
      "moderator",
      "canMoveToBreakoutRooms"
    );

    if (!canMove) {
      throw new Error("Insufficient permissions to move participants");
    }

    const breakoutRoom = await ctx.runQuery(internal.breakoutRooms.getById, {
      id: args.breakoutRoomId,
    });

    if (!breakoutRoom) {
      throw new Error("Breakout room not found");
    }

    // Move each participant
    for (const participantId of args.participantIds) {
      await ctx.runMutation(internal.participants.moveToBreakoutRoom, {
        participantId,
        breakoutRoomId: args.breakoutRoomId,
        moderatorId: args.moderatorId,
      });

      // Generate token for breakout room
      const participant = await ctx.runQuery(internal.participants.getById, {
        id: participantId,
      });

      if (participant) {
        const token = await ctx.runAction(internal.livekit.generateToken, {
          roomName: breakoutRoom.livekitRoomName,
          identity: participant.userId,
          name: participant.name,
        });

        // Send token to participant (you'd implement this via websocket/notification)
        await ctx.runMutation(internal.notifications.sendBreakoutRoomInvite, {
          participantId,
          breakoutRoomId: args.breakoutRoomId,
          token,
        });
      }
    }

    return { success: true };
  },
});

// Internal mutations
export const applyMute = mutation({
  args: {
    participantId: v.id("participants"),
    moderatorId: v.string(),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.get(args.participantId);
    if (!participant) return;

    // Update participant restrictions
    await ctx.db.patch(args.participantId, {
      restrictions: {
        ...participant.restrictions,
        isMuted: true,
      },
      moderationHistory: [
        ...participant.moderationHistory,
        {
          action: "muted",
          moderatorId: args.moderatorId,
          reason: args.reason,
          timestamp: Date.now(),
          duration: args.duration,
        },
      ],
    });

    // Log the action
    await ctx.db.insert("moderationLogs", {
      streamId: participant.streamId,
      moderatorId: args.moderatorId,
      targetUserId: participant.userId,
      action: "mute",
      reason: args.reason,
      duration: args.duration,
      timestamp: Date.now(),
    });
  },
});

export const applyTimeout = mutation({
  args: {
    participantId: v.id("participants"),
    moderatorId: v.string(),
    timeoutUntil: v.number(),
    reason: v.optional(v.string()),
    duration: v.number(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.get(args.participantId);
    if (!participant) return;

    await ctx.db.patch(args.participantId, {
      status: "left",
      restrictions: {
        ...participant.restrictions,
        timeoutUntil: args.timeoutUntil,
      },
      moderationHistory: [
        ...participant.moderationHistory,
        {
          action: "timeout",
          moderatorId: args.moderatorId,
          reason: args.reason,
          timestamp: Date.now(),
          duration: args.duration,
        },
      ],
    });

    await ctx.db.insert("moderationLogs", {
      streamId: participant.streamId,
      moderatorId: args.moderatorId,
      targetUserId: participant.userId,
      action: "timeout",
      reason: args.reason,
      duration: args.duration,
      timestamp: Date.now(),
    });
  },
});


Enhanced LiveKit Service

// convex/lib/livekit.ts (Enhanced)
import { AccessToken, RoomServiceClient } from "livekit-server-sdk";

export class LiveKitService {
  private config: LiveKitConfig;
  private roomService: RoomServiceClient;

  constructor(config: LiveKitConfig) {
    this.config = config;
    this.roomService = new RoomServiceClient(
      `https://${config.host}`,
      config.apiKey,
      config.apiSecret
    );
  }

  // ... existing methods ...

  async muteParticipant(roomName: string, participantIdentity: string): Promise<void> {
    await this.roomService.mutePublishedTrack(
      roomName,
      participantIdentity,
      "", // trackSid - empty means all audio tracks
      true // muted
    );
  }

  async unmuteParticipant(roomName: string, participantIdentity: string): Promise<void> {
    await this.roomService.mutePublishedTrack(
      roomName,
      participantIdentity,
      "",
      false
    );
  }

  async disableParticipantVideo(roomName: string, participantIdentity: string): Promise<void> {
    await this.roomService.mutePublishedTrack(
      roomName,
      participantIdentity,
      "", // empty means all video tracks
      true
    );
  }

  async enableParticipantVideo(roomName: string, participantIdentity: string): Promise<void> {
    await this.roomService.mutePublishedTrack(
      roomName,
      participantIdentity,
      "",
      false
    );
  }

  async disconnectParticipant(roomName: string, participantIdentity: string): Promise<void> {
    await this.roomService.removeParticipant(roomName, participantIdentity);
  }

  async updateParticipantPermissions(
    roomName: string,
    participantIdentity: string,
    permissions: {
      canPublish?: boolean;
      canSubscribe?: boolean;
      canPublishData?: boolean;
    }
  ): Promise<void> {
    await this.roomService.updateParticipant(roomName, participantIdentity, {
      permission: {
        canPublish: permissions.canPublish,
        canSubscribe: permissions.canSubscribe,
        canPublishData: permissions.canPublishData,
      },
    });
  }

  generateTokenWithPermissions(
    roomName: string,
    identity: string,
    name: string,
    permissions: {
      canPublish: boolean;
      canSubscribe: boolean;
      canPublishData: boolean;
      hidden: boolean;
      recorder: boolean;
    }
  ): string {
    const token = new AccessToken(
      this.config.apiKey,
      this.config.apiSecret,
      {
        identity,
        name,
      }
    );

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: permissions.canPublish,
      canSubscribe: permissions.canSubscribe,
      canPublishData: permissions.canPublishData,
      hidden: permissions.hidden,
      recorder: permissions.recorder,
    });

    return token.toJwt();
  }
}



React Components for Moderation

// src/components/moderation/ModerationPanel.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface ModerationPanelProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function ModerationPanel({ streamId, currentUserId }: ModerationPanelProps) {
  const participants = useQuery(api.participants.getByStreamId, { streamId });
  const moderationLogs = useQuery(api.moderation.getLogs, { streamId });
  
  const muteParticipant = useMutation(api.moderation.muteParticipant);
  const timeoutParticipant = useMutation(api.moderation.timeoutParticipant);
  const createBreakoutRoom = useMutation(api.moderation.createBreakoutRoom);
  
  const [selectedParticipants, setSelectedParticipants] = useState<Id<"participants">[]>([]);
  const [showTimeoutModal, setShowTimeoutModal] = useState(false);
  const [showBreakoutModal, setShowBreakoutModal] = useState(false);

  const handleMute = async (participantId: Id<"participants">) => {
    const participant = participants?.find(p => p._id === participantId);
    if (!participant) return;

    await muteParticipant({
      streamId,
      targetUserId: participant.userId,
      moderatorId: currentUserId,
      reason: "Moderator action",
    });
  };

  const handleTimeout = async (participantId: Id<"participants">, duration: number, reason?: string) => {
    const participant = participants?.find(p => p._id === participantId);
    if (!participant) return;

    await timeoutParticipant({
      streamId,
      targetUserId: participant.userId,
      moderatorId: currentUserId,
      duration,
      reason,
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold mb-4">Moderation Panel</h3>
      
      {/* Participants List */}
      <div className="space-y-2 mb-6">
        <h4 className="font-medium">Participants</h4>
        {participants?.map((participant) => (
          <div key={participant._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={selectedParticipants.includes(participant._id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedParticipants([...selectedParticipants, participant._id]);
                  } else {
                    setSelectedParticipants(selectedParticipants.filter(id => id !== participant._id));
                  }
                }}
              />
              <span className="font-medium">{participant.name}</span>
              <span className={`px-2 py-1 text-xs rounded ${
                participant.role === 'host' ? 'bg-purple-100 text-purple-800' :
                participant.role === 'moderator' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {participant.role}
              </span>
              {participant.restrictions.isMuted && (
                <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Muted</span>
              )}
              {participant.restrictions.timeoutUntil && participant.restrictions.timeoutUntil > Date.now() && (
                <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">Timeout</span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => handleMute(participant._id)}
                className="px-3 py-1 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600"
                disabled={participant.restrictions.isMuted}
              >
                {participant.restrictions.isMuted ? 'Muted' : 'Mute'}
              </button>
              <button
                onClick={() => setShowTimeoutModal(true)}
                className="px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600"
              >
                Timeout
              </button>
              <button
                className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
              >
                Kick
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      {selectedParticipants.length > 0 && (
        <div className="mb-6 p-4 bg-blue-50 rounded">
          <h4 className="font-medium mb-2">{selectedParticipants.length} participants selected</h4>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowBreakoutModal(true)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Move to Breakout Room
            </button>
            <button className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
              Mute All
            </button>
          </div>
        </div>
      )}

      {/* Moderation Logs */}
      <div>
        <h4 className="font-medium mb-2">Recent Actions</h4>
        <div className="max-h-40 overflow-y-auto space-y-1">
          {moderationLogs?.slice(0, 10).map((log) => (
            <div key={log._id} className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
              <span className="font-medium">{log.action}</span> - {log.targetUserId}
              {log.reason && <span className="text-gray-500"> ({log.reason})</span>}
              <span className="text-xs text-gray-400 ml-2">
                {new Date(log.timestamp).toLocaleTimeString()}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Modals would go here */}
      {showTimeoutModal && (
        <TimeoutModal
          onClose={() => setShowTimeoutModal(false)}
          onTimeout={handleTimeout}
          participantId={selectedParticipants[0]} // For simplicity
        />
      )}
      
      {showBreakoutModal && (
        <BreakoutRoomModal
          streamId={streamId}
          selectedParticipants={selectedParticipants}
          onClose={() => setShowBreakoutModal(false)}
          currentUserId={currentUserId}
        />
      )}
    </div>
  );
}


Breakout Room Components

// src/components/moderation/BreakoutRoomModal.tsx
import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface BreakoutRoomModalProps {
  streamId: Id<"streams">;
  selectedParticipants: Id<"participants">[];
  onClose: () => void;
  currentUserId: string;
}

export function BreakoutRoomModal({ 
  streamId, 
  selectedParticipants, 
  onClose, 
  currentUserId 
}: BreakoutRoomModalProps) {
  const [roomName, setRoomName] = useState('');
  const [maxParticipants, setMaxParticipants] = useState(10);
  const [autoReturn, setAutoReturn] = useState(true);
  const [autoReturnAfter, setAutoReturnAfter] = useState(15);
  const [isCreating, setIsCreating] = useState(false);

  const createBreakoutRoom = useMutation(api.moderation.createBreakoutRoom);
  const moveToBreakoutRoom = useMutation(api.moderation.moveToBreakoutRoom);
  const existingRooms = useQuery(api.breakoutRooms.getByStreamId, { streamId });

  const handleCreateAndMove = async () => {
    if (!roomName.trim()) return;
    
    setIsCreating(true);
    try {
      const { breakoutRoomId } = await createBreakoutRoom({
        streamId,
        name: roomName,
        createdBy: currentUserId,
        maxParticipants,
        autoReturn,
        autoReturnAfter: autoReturn ? autoReturnAfter : undefined,
      });

      await moveToBreakoutRoom({
        streamId,
        participantIds: selectedParticipants,
        breakoutRoomId,
        moderatorId: currentUserId,
      });

      onClose();
    } catch (error) {
      console.error('Failed to create breakout room:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleMoveToExisting = async (breakoutRoomId: Id<"breakoutRooms">) => {
    try {
      await moveToBreakoutRoom({
        streamId,
        participantIds: selectedParticipants,
        breakoutRoomId,
        moderatorId: currentUserId,
      });
      onClose();
    } catch (error) {
      console.error('Failed to move participants:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Breakout Room Options</h3>
        
        {/* Existing Rooms */}
        {existingRooms && existingRooms.length > 0 && (
          <div className="mb-6">
            <h4 className="font-medium mb-2">Move to Existing Room</h4>
            <div className="space-y-2">
              {existingRooms.map((room) => (
                <button
                  key={room._id}
                  onClick={() => handleMoveToExisting(room._id)}
                  className="w-full text-left p-3 border rounded hover:bg-gray-50"
                >
                  <div className="font-medium">{room.name}</div>
                  <div className="text-sm text-gray-500">
                    {room.status === 'active' ? 'Active' : 'Closed'} • 
                    Max {room.settings.maxParticipants} participants
                  </div>
                </button>
              ))}
            </div>
            <div className="my-4 border-t pt-4">
              <p className="text-sm text-gray-500 mb-2">Or create a new room:</p>
            </div>
          </div>
        )}

        {/* Create New Room */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Room Name</label>
            <input
              type="text"
              value={roomName}
              onChange={(e) => setRoomName(e.target.value)}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              placeholder="Enter room name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Max Participants</label>
            <input
              type="number"
              value={maxParticipants}
              onChange={(e) => setMaxParticipants(parseInt(e.target.value))}
              min="2"
              max="50"
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoReturn"
              checked={autoReturn}
              onChange={(e) => setAutoReturn(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="autoReturn" className="text-sm">
              Auto-return participants to main room
            </label>
          </div>

          {autoReturn && (
            <div>
              <label className="block text-sm font-medium mb-1">
                Auto-return after (minutes)
              </label>
              <input
                type="number"
                value={autoReturnAfter}
                onChange={(e) => setAutoReturnAfter(parseInt(e.target.value))}
                min="1"
                max="120"
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleCreateAndMove}
            disabled={!roomName.trim() || isCreating}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isCreating ? 'Creating...' : 'Create & Move'}
          </button>
        </div>
      </div>
    </div>
  );
}
// src/components/moderation/BreakoutRoomManager.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface BreakoutRoomManagerProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function BreakoutRoomManager({ streamId, currentUserId }: BreakoutRoomManagerProps) {
  const breakoutRooms = useQuery(api.breakoutRooms.getByStreamId, { streamId });
  const participants = useQuery(api.participants.getByStreamId, { streamId });
  const closeBreakoutRoom = useMutation(api.moderation.closeBreakoutRoom);
  const returnFromBreakout = useMutation(api.moderation.returnFromBreakout);

  const [expandedRoom, setExpandedRoom] = useState<Id<"breakoutRooms"> | null>(null);

  const getParticipantsInRoom = (roomId: Id<"breakoutRooms">) => {
    return participants?.filter(p => p.restrictions.breakoutRoomId === roomId) || [];
  };

  const handleCloseRoom = async (roomId: Id<"breakoutRooms">) => {
    try {
      await closeBreakoutRoom({
        breakoutRoomId: roomId,
        moderatorId: currentUserId,
      });
    } catch (error) {
      console.error('Failed to close breakout room:', error);
    }
  };

  const handleReturnParticipant = async (participantId: Id<"participants">) => {
    try {
      await returnFromBreakout({
        participantId,
        moderatorId: currentUserId,
      });
    } catch (error) {
      console.error('Failed to return participant:', error);
    }
  };

  if (!breakoutRooms || breakoutRooms.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="font-semibold mb-2">Breakout Rooms</h3>
        <p className="text-gray-500 text-sm">No breakout rooms created yet.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="font-semibold mb-4">Breakout Rooms ({breakoutRooms.length})</h3>
      
      <div className="space-y-3">
        {breakoutRooms.map((room) => {
          const roomParticipants = getParticipantsInRoom(room._id);
          const isExpanded = expandedRoom === room._id;
          
          return (
            <div key={room._id} className="border rounded-lg">
              <div 
                className="p-3 cursor-pointer hover:bg-gray-50"
                onClick={() => setExpandedRoom(isExpanded ? null : room._id)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{room.name}</h4>
                    <p className="text-sm text-gray-500">
                      {roomParticipants.length} / {room.settings.maxParticipants} participants
                      {room.settings.autoReturn && (
                        <span> • Auto-return in {room.settings.autoReturnAfter}min</span>
                      )}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded ${
                      room.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {room.status}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCloseRoom(room._id);
                      }}
                      className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
              
              {isExpanded && (
                <div className="border-t p-3 bg-gray-50">
                  <h5 className="font-medium mb-2">Participants in this room:</h5>
                  {roomParticipants.length > 0 ? (
                    <div className="space-y-2">
                      {roomParticipants.map((participant) => (
                        <div key={participant._id} className="flex items-center justify-between">
                          <span className="text-sm">{participant.name}</span>
                          <button
                            onClick={() => handleReturnParticipant(participant._id)}
                            className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                          >
                            Return to Main
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No participants in this room</p>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}


Timeout Modal Component

// src/components/moderation/TimeoutModal.tsx
import React, { useState } from 'react';
import { Id } from '../../../convex/_generated/dataModel';

interface TimeoutModalProps {
  participantId: Id<"participants">;
  onClose: () => void;
  onTimeout: (participantId: Id<"participants">, duration: number, reason?: string) => Promise<void>;
}

export function TimeoutModal({ participantId, onClose, onTimeout }: TimeoutModalProps) {
  const [duration, setDuration] = useState(5);
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const presetDurations = [
    { label: '1 minute', value: 1 },
    { label: '5 minutes', value: 5 },
    { label: '10 minutes', value: 10 },
    { label: '30 minutes', value: 30 },
    { label: '1 hour', value: 60 },
  ];

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onTimeout(participantId, duration, reason || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to timeout participant:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Timeout Participant</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Duration</label>
            <div className="grid grid-cols-2 gap-2 mb-2">
              {presetDurations.map((preset) => (
                <button
                  key={preset.value}
                  onClick={() => setDuration(preset.value)}
                  className={`p-2 text-sm border rounded ${
                    duration === preset.value
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {preset.label}
                </button>
              ))}
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value) || 1)}
                min="1"
                max="1440"
                className="flex-1 p-2 border rounded focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-500">minutes</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Reason (optional)</label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Enter reason for timeout..."
            />
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
            <p className="text-sm text-yellow-800">
              The participant will be disconnected and unable to rejoin for {duration} minute{duration !== 1 ? 's' : ''}.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
          >
            {isSubmitting ? 'Processing...' : 'Apply Timeout'}
          </button>
        </div>
      </div>
    </div>
  );
}

Advanced Permission Control Component

// src/components/moderation/PermissionControl.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface PermissionControlProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function PermissionControl({ streamId, currentUserId }: PermissionControlProps) {
  const stream = useQuery(api.streams.getById, { id: streamId });
  const participants = useQuery(api.participants.getByStreamId, { streamId });
  const updateStreamSettings = useMutation(api.streams.updateSettings);
  const updateParticipantPermissions = useMutation(api.moderation.updateParticipantPermissions);

  const [globalSettings, setGlobalSettings] = useState({
    allowScreenShare: stream?.settings.allowScreenShare ?? true,
    allowChat: stream?.settings.allowChat ?? true,
    requireApprovalToJoin: stream?.settings.requireApprovalToJoin ?? false,
  });

  const handleGlobalSettingChange = async (setting: keyof typeof globalSettings, value: boolean) => {
    const newSettings = { ...globalSettings, [setting]: value };
    setGlobalSettings(newSettings);
    
    await updateStreamSettings({
      streamId,
      settings: {
        ...stream?.settings,
        [setting]: value,
      },
    });
  };

  const handleParticipantPermissionChange = async (
    participantId: Id<"participants">,
    permission: string,
    value: boolean
  ) => {
    await updateParticipantPermissions({
      participantId,
      permissions: { [permission]: value },
      moderatorId: currentUserId,
    });
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold mb-4">Permission Control</h3>
      
      {/* Global Stream Settings */}
      <div className="mb-6">
        <h4 className="font-medium mb-3">Global Stream Settings</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Allow Screen Sharing</label>
              <p className="text-sm text-gray-500">Participants can share their screen</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.allowScreenShare}
                onChange={(e) => handleGlobalSettingChange('allowScreenShare', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Allow Chat</label>
              <p className="text-sm text-gray-500">Participants can send chat messages</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.allowChat}
                onChange={(e) => handleGlobalSettingChange('allowChat', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Require Approval to Join</label>
              <p className="text-sm text-gray-500">New participants need approval before joining</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={globalSettings.requireApprovalToJoin}
                onChange={(e) => handleGlobalSettingChange('requireApprovalToJoin', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Individual Participant Permissions */}
      <div>
        <h4 className="font-medium mb-3">Individual Permissions</h4>
        <div className="space-y-3">
          {participants?.filter(p => p.role !== 'host').map((participant) => (
            <div key={participant._id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h5 className="font-medium">{participant.name}</h5>
                  <span className={`text-xs px-2 py-1 rounded ${
                    participant.role === 'moderator' ? 'bg-blue-100 text-blue-800' :
                    participant.role === 'co_host' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {participant.role}
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Speak</span>
                  <input
                    type="checkbox"
                    checked={participant.permissions.canSpeak}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant._id, 
                      'canSpeak', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Video</span>
                  <input
                    type="checkbox"
                    checked={participant.permissions.canVideo}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant._id, 
                      'canVideo', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Screen Share</span>
                  <input
                    type="checkbox"
                    checked={participant.permissions.canScreenShare}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant._id, 
                      'canScreenShare', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Can Chat</span>
                  <input
                    type="checkbox"
                    checked={participant.permissions.canChat}
                    onChange={(e) => handleParticipantPermissionChange(
                      participant._id, 
                      'canChat', 
                      e.target.checked
                    )}
                    className="rounded"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

Additional Convex Functions for Advanced Features

// convex/moderation.ts (Additional functions)

export const closeBreakoutRoom = action({
  args: {
    breakoutRoomId: v.id("breakoutRooms"),
    moderatorId: v.string(),
  },
  handler: async (ctx, args) => {
    const breakoutRoom = await ctx.runQuery(internal.breakoutRooms.getById, {
      id: args.breakoutRoomId,
    });

    if (!breakoutRoom) {
      throw new Error("Breakout room not found");
    }

    // Return all participants to main room
    const participants = await ctx.runQuery(internal.participants.getByBreakoutRoom, {
      breakoutRoomId: args.breakoutRoomId,
    });

    for (const participant of participants) {
      await ctx.runMutation(internal.participants.returnFromBreakoutRoom, {
        participantId: participant._id,
        moderatorId: args.moderatorId,
      });
    }

    // Close the room
    await ctx.runMutation(internal.breakoutRooms.close, {
      breakoutRoomId: args.breakoutRoomId,
    });

    return { success: true };
  },
});

export const returnFromBreakout = action({
  args: {
    participantId: v.id("participants"),
    moderatorId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.runQuery(internal.participants.getById, {
      id: args.participantId,
    });

    if (!participant || !participant.restrictions.breakoutRoomId) {
      throw new Error("Participant not in breakout room");
    }

    const stream = await ctx.runQuery(internal.streams.getById, {
      id: participant.streamId,
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    // Generate token for main room
    const token = await ctx.runAction(internal.livekit.generateToken, {
      roomName: stream.livekitRoomName,
      identity: participant.userId,
      name: participant.name,
    });

    // Update participant status
    await ctx.runMutation(internal.participants.returnFromBreakoutRoom, {
      participantId: args.participantId,
      moderatorId: args.moderatorId,
    });

    // Send notification to participant with new token
    await ctx.runMutation(internal.notifications.sendRoomTransfer, {
      participantId: args.participantId,
      token,
      roomType: "main",
    });

    return { success: true };
  },
});

export const updateParticipantPermissions = mutation({
  args: {
    participantId: v.id("participants"),
    permissions: v.object({
      canSpeak: v.optional(v.boolean()),
      canVideo: v.optional(v.boolean()),
      canScreenShare: v.optional(v.boolean()),
      canChat: v.optional(v.boolean()),
      canInviteOthers: v.optional(v.boolean()),
      canModerate: v.optional(v.boolean()),
    }),
    moderatorId: v.string(),
  },
  handler: async (ctx, args) => {
    const participant = await ctx.db.get(args.participantId);
    if (!participant) {
      throw new Error("Participant not found");
    }

    const updatedPermissions = {
      ...participant.permissions,
      ...args.permissions,
    };

    await ctx.db.patch(args.participantId, {
      permissions: updatedPermissions,
    });

    // Log the permission change
    await ctx.db.insert("moderationLogs", {
      streamId: participant.streamId,
      moderatorId: args.moderatorId,
      targetUserId: participant.userId,
      action: "update_permissions",
      metadata: {
        permissions: args.permissions,
      },
      timestamp: Date.now(),
    });
  },
});

export const getLogs = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("moderationLogs")
      .withIndex("by_streamId", (q) => q.eq("streamId", args.streamId))
      .order("desc")
      .take(50);
  },
});

Main Studio Component Integration

// src/components/studio/StreamStudio.tsx
import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { ModerationPanel } from '../moderation/ModerationPanel';
import { BreakoutRoomManager } from '../moderation/BreakoutRoomManager';
import { PermissionControl } from '../moderation/PermissionControl';
import { LiveKitRoom } from '../livekit/LiveKitRoom';

interface StreamStudioProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function StreamStudio({ streamId, currentUserId }: StreamStudioProps) {
  const stream = useQuery(api.streams.getById, { id: streamId });
  const currentUser = useQuery(api.users.getByUserId, { userId: currentUserId });
  const [activeTab, setActiveTab] = useState<'studio' | 'moderation' | 'breakout' | 'permissions'>('studio');

  if (!stream || !currentUser) {
    return <div>Loading...</div>;
  }

  const canModerate = currentUser.globalRole === 'master' || 
                     currentUser.globalRole === 'admin' || 
                     stream.hostId === currentUserId;

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">{stream.title}</h1>
            <p className="text-sm text-gray-500">
              Status: <span className={`font-medium ${
                stream.status === 'live' ? 'text-green-600' :
                stream.status === 'pending' ? 'text-yellow-600' :
                'text-gray-600'
              }`}>
                {stream.status}
              </span>
            </p>
          </div>
          
          {canModerate && (
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('studio')}
                className={`px-4 py-2 rounded ${
                  activeTab === 'studio' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Studio
              </button>
              <button
                onClick={() => setActiveTab('moderation')}
                className={`px-4 py-2 rounded ${
                  activeTab === 'moderation' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Moderation
              </button>
              <button
                onClick={() => setActiveTab('breakout')}
                className={`px-4 py-2 rounded ${
                  activeTab === 'breakout' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Breakout Rooms
              </button>
              <button
                onClick={() => setActiveTab('permissions')}
                className={`px-4 py-2 rounded ${
                  activeTab === 'permissions' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Permissions
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Left Panel - Video/Studio */}
        <div className="flex-1 p-6">
          {activeTab === 'studio' && (
            <LiveKitRoom 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
          {activeTab === 'moderation' && canModerate && (
            <ModerationPanel 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
          {activeTab === 'breakout' && canModerate && (
            <BreakoutRoomManager 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
          {activeTab === 'permissions' && canModerate && (
            <PermissionControl 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
        </div>

        {/* Right Panel - Always visible for basic controls */}
        <div className="w-80 bg-white border-l p-4 space-y-4">
          {/* Stream Controls */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium mb-2">Stream Controls</h3>
            {/* Add your stream control buttons here */}
          </div>

          {/* Chat */}
          <div className="bg-gray-50 rounded-lg p-4 flex-1">
            <h3 className="font-medium mb-2">Chat</h3>
            {/* Add your chat component here */}
          </div>
        </div>
      </div>
    </div>
  );
}