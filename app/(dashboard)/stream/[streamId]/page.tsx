"use client";

import { useParams } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { StreamStudio } from "@/components/studio/StreamStudio";

export default function StreamPage() {
  const params = useParams();
  const streamId = params.streamId as string;
  const { user } = useUser();

  if (!user) {
    return <div>Please sign in to access the stream.</div>;
  }

  return (
    <StreamStudio 
      streamId={streamId as any} 
      currentUserId={user.id}
    />
  );
}
