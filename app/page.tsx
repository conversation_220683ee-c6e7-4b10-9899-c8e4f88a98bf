"use client";

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Video, Users, Calendar, Settings } from "lucide-react";

export default function Home() {
  const { user } = useUser();
  const myRooms = useQuery(api.rooms.getMyRooms);
  const [newRoomName, setNewRoomName] = useState("");
  const router = useRouter();

  const handleCreateRoom = () => {
    if (newRoomName.trim()) {
      const roomId = newRoomName.trim().toLowerCase().replace(/\s+/g, '-');
      router.push(`/rooms/${roomId}`);
    }
  };

  const handleJoinRoom = () => {
    if (newRoomName.trim()) {
      const roomId = newRoomName.trim().toLowerCase().replace(/\s+/g, '-');
      router.push(`/rooms/${roomId}`);
    }
  };

  if (!user) {
    return (
      <main className="min-h-screen bg-gray-50">
        <div className="flex min-h-screen flex-col">
          {/* Header */}
          <header className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="max-w-7xl mx-auto flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Video className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">Meet Clone</span>
              </div>
              <div className="flex items-center space-x-4">
                <Link
                  href="/sign-in"
                  className="text-gray-600 hover:text-gray-900 font-medium"
                >
                  Sign In
                </Link>
                <Link
                  href="/sign-up"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Get Started
                </Link>
              </div>
            </div>
          </header>

          {/* Hero Section */}
          <div className="flex-1 flex items-center justify-center px-6 py-12">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-5xl font-bold text-gray-900 mb-6">
                Video meetings for everyone
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings.
              </p>
              <div className="flex gap-4 justify-center">
                <Link
                  href="/sign-up"
                  className="px-8 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Start a meeting
                </Link>
                <Link
                  href="/sign-in"
                  className="px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                >
                  Sign In
                </Link>
              </div>

              {/* Features */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Video className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">HD Video & Audio</h3>
                  <p className="text-gray-600">Crystal clear video and audio quality for professional meetings.</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Up to 100 participants</h3>
                  <p className="text-gray-600">Host large meetings with participants from around the world.</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Easy to use</h3>
                  <p className="text-gray-600">Simple interface that works on any device, no downloads required.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 md:px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">Meet Clone</span>
          </div>
          <div className="flex items-center space-x-2 md:space-x-4">
            <span className="text-gray-600 hidden md:inline">Welcome back, {user.firstName}</span>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 md:px-6 py-12">
        {/* Main Meeting Actions */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Premium video meetings.
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Now free for everyone.
          </p>
        </div>

        {/* Meeting Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* New Meeting */}
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <Video className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">New Meeting</h3>
            <p className="text-gray-600 mb-6">Start an instant meeting</p>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Enter a room name"
                value={newRoomName}
                onChange={(e) => setNewRoomName(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                onKeyPress={(e) => e.key === 'Enter' && handleCreateRoom()}
              />
              <button
                onClick={handleCreateRoom}
                disabled={!newRoomName.trim()}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Start meeting
              </button>
            </div>
          </div>

          {/* Join Meeting */}
          <div className="bg-white rounded-lg border border-gray-200 p-8">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Join Meeting</h3>
            <p className="text-gray-600 mb-6">Join with a meeting ID</p>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Enter meeting ID or room name"
                value={newRoomName}
                onChange={(e) => setNewRoomName(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                onKeyPress={(e) => e.key === 'Enter' && handleJoinRoom()}
              />
              <button
                onClick={handleJoinRoom}
                disabled={!newRoomName.trim()}
                className="w-full px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Join
              </button>
            </div>
          </div>
        </div>

        {/* Recent Meetings */}
        {myRooms && myRooms.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent meetings</h3>
            <div className="space-y-3">
              {myRooms.slice(0, 5).map((room: any) => (
                <div key={room._id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Video className="w-4 h-4 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{room.name}</p>
                      <p className="text-sm text-gray-500">{new Date(room._creationTime).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <Link
                    href={`/rooms/${room.name}`}
                    className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg font-medium transition-colors"
                  >
                    Join
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
