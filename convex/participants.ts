import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const joinStream = mutation({
  args: {
    streamId: v.id("streams"),
    requestedRole: v.optional(v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get user info
    const user = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    if (user.isBanned) {
      throw new Error("User is banned");
    }

    // Get stream info
    const stream = await ctx.db.get(args.streamId);
    if (!stream) {
      throw new Error("Stream not found");
    }

    // Check if user is already a participant
    const existingParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", identity.subject).eq("streamId", args.streamId)
      )
      .first();

    if (existingParticipant) {
      // Reactivate if inactive
      if (!existingParticipant.isActive) {
        await ctx.db.patch(existingParticipant._id, {
          isActive: true,
          joinedAt: Date.now(),
        });
      }
      return existingParticipant._id;
    }

    // Determine role
    let role = args.requestedRole || "viewer";
    
    // Stream host gets host role automatically
    if (stream.hostId === identity.subject) {
      role = "host";
    } else if (args.requestedRole && args.requestedRole !== "viewer") {
      // For elevated roles, check if user has global permissions
      if (user.globalRole === "master") {
        role = args.requestedRole;
      } else if (user.globalRole === "admin" && args.requestedRole !== "host") {
        role = args.requestedRole;
      } else {
        // Default to viewer if no permissions
        role = "viewer";
      }
    }

    // Create participant record
    const participantId = await ctx.db.insert("streamParticipants", {
      streamId: args.streamId,
      userId: identity.subject,
      role: role,
      joinedAt: Date.now(),
      isActive: true,
    });

    return participantId;
  },
});

export const leaveStream = mutation({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const participant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", identity.subject).eq("streamId", args.streamId)
      )
      .first();

    if (participant) {
      await ctx.db.patch(participant._id, {
        isActive: false,
      });
    }

    return { success: true };
  },
});

export const getStreamParticipants = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const participants = await ctx.db
      .query("streamParticipants")
      .withIndex("by_stream", (q) => q.eq("streamId", args.streamId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get user info for each participant
    const participantsWithUsers = await Promise.all(
      participants.map(async (participant) => {
        const user = await ctx.db
          .query("users")
          .withIndex("by_user_id", (q) => q.eq("userId", participant.userId))
          .first();
        
        return {
          ...participant,
          user: user ? {
            username: user.username,
            globalRole: user.globalRole,
            isBanned: user.isBanned,
          } : null,
        };
      })
    );

    return participantsWithUsers;
  },
});

export const updateParticipantRole = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    newRole: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get current user's participation
    const currentParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", identity.subject).eq("streamId", args.streamId)
      )
      .first();

    if (!currentParticipant) {
      throw new Error("Not a participant in this stream");
    }

    // Check permissions
    if (currentParticipant.role !== "host" && currentParticipant.role !== "co-host") {
      // Check global permissions
      const currentUser = await ctx.db
        .query("users")
        .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
        .first();

      if (!currentUser || (currentUser.globalRole !== "master" && currentUser.globalRole !== "admin")) {
        throw new Error("Insufficient permissions");
      }
    }

    // Get target participant
    const targetParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.targetUserId).eq("streamId", args.streamId)
      )
      .first();

    if (!targetParticipant) {
      throw new Error("Target user is not a participant");
    }

    // Update role
    await ctx.db.patch(targetParticipant._id, {
      role: args.newRole,
    });

    return { success: true };
  },
});

export const getParticipantRole = query({
  args: {
    streamId: v.id("streams"),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const targetUserId = args.userId || identity.subject;
    
    const participant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", targetUserId).eq("streamId", args.streamId)
      )
      .first();

    return participant?.role || null;
  },
});

export const getParticipant = query({
  args: {
    streamId: v.id("streams"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) =>
        q.eq("userId", args.userId).eq("streamId", args.streamId)
      )
      .first();
  },
});