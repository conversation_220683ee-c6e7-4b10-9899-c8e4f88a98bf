import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import type { MutationCtx, QueryCtx } from "./_generated/server";

async function checkModerationPermissions(
  ctx: MutationCtx,
  streamId: any,
  requiredRoles: string[] = ["host", "co-host", "moderator"]
) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  // Get participant role
  const participant = await ctx.db
    .query("streamParticipants")
    .withIndex("by_user_stream", (q) => 
      q.eq("userId", identity.subject).eq("streamId", streamId)
    )
    .first();

  if (!participant) {
    throw new Error("Not a participant in this stream");
  }

  // Check if user has required stream role
  if (requiredRoles.includes(participant.role)) {
    return { userId: identity.subject, role: participant.role };
  }

  // Check global permissions as fallback
  const user = await ctx.db
    .query("users")
    .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
    .first();

  if (user && (user.globalRole === "master" || user.globalRole === "admin")) {
    return { userId: identity.subject, role: participant.role, globalRole: user.globalRole };
  }

  throw new Error("Insufficient permissions for moderation");
}

export const muteParticipant = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    duration: v.optional(v.number()), // Duration in minutes, undefined for permanent
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await checkModerationPermissions(ctx, args.streamId);

    // Check if target is a participant
    const targetParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.targetUserId).eq("streamId", args.streamId)
      )
      .first();

    if (!targetParticipant) {
      throw new Error("Target user is not a participant");
    }

    // Prevent moderating higher-level roles (unless global admin/master)
    if (
      (targetParticipant.role === "host" || targetParticipant.role === "co-host") &&
      moderator.role !== "host" &&
      !moderator.globalRole
    ) {
      throw new Error("Cannot moderate hosts or co-hosts");
    }

    // Log the moderation action
    await ctx.db.insert("moderationLogs", {
      streamId: args.streamId,
      moderatorId: moderator.userId,
      targetUserId: args.targetUserId,
      action: "mute",
      reason: args.reason,
      duration: args.duration,
      timestamp: Date.now(),
    });

    return { success: true, duration: args.duration };
  },
});

export const unmuteParticipant = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await checkModerationPermissions(ctx, args.streamId);

    // Log the moderation action
    await ctx.db.insert("moderationLogs", {
      streamId: args.streamId,
      moderatorId: moderator.userId,
      targetUserId: args.targetUserId,
      action: "unmute",
      reason: args.reason,
      timestamp: Date.now(),
    });

    return { success: true };
  },
});

export const timeoutParticipant = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    duration: v.number(), // Duration in minutes
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await checkModerationPermissions(ctx, args.streamId);

    // Check if target is a participant
    const targetParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.targetUserId).eq("streamId", args.streamId)
      )
      .first();

    if (!targetParticipant) {
      throw new Error("Target user is not a participant");
    }

    // Prevent moderating higher-level roles
    if (
      (targetParticipant.role === "host" || targetParticipant.role === "co-host") &&
      moderator.role !== "host" &&
      !moderator.globalRole
    ) {
      throw new Error("Cannot timeout hosts or co-hosts");
    }

    // Deactivate participant temporarily
    await ctx.db.patch(targetParticipant._id, {
      isActive: false,
    });

    // Log the moderation action
    await ctx.db.insert("moderationLogs", {
      streamId: args.streamId,
      moderatorId: moderator.userId,
      targetUserId: args.targetUserId,
      action: "timeout",
      reason: args.reason,
      duration: args.duration,
      timestamp: Date.now(),
    });

    return { success: true, duration: args.duration };
  },
});

export const kickParticipant = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await checkModerationPermissions(ctx, args.streamId);

    // Check if target is a participant
    const targetParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.targetUserId).eq("streamId", args.streamId)
      )
      .first();

    if (!targetParticipant) {
      throw new Error("Target user is not a participant");
    }

    // Prevent moderating higher-level roles
    if (
      (targetParticipant.role === "host" || targetParticipant.role === "co-host") &&
      moderator.role !== "host" &&
      !moderator.globalRole
    ) {
      throw new Error("Cannot kick hosts or co-hosts");
    }

    // Remove participant from stream
    await ctx.db.patch(targetParticipant._id, {
      isActive: false,
    });

    // Log the moderation action
    await ctx.db.insert("moderationLogs", {
      streamId: args.streamId,
      moderatorId: moderator.userId,
      targetUserId: args.targetUserId,
      action: "kick",
      reason: args.reason,
      timestamp: Date.now(),
    });

    return { success: true };
  },
});

export const banParticipant = mutation({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.string(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const moderator = await checkModerationPermissions(ctx, args.streamId, ["host", "co-host"]);

    // Check if target is a participant
    const targetParticipant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", args.targetUserId).eq("streamId", args.streamId)
      )
      .first();

    if (!targetParticipant) {
      throw new Error("Target user is not a participant");
    }

    // Prevent moderating higher-level roles
    if (
      (targetParticipant.role === "host" || targetParticipant.role === "co-host") &&
      moderator.role !== "host" &&
      !moderator.globalRole
    ) {
      throw new Error("Cannot ban hosts or co-hosts");
    }

    // Remove participant from stream
    await ctx.db.patch(targetParticipant._id, {
      isActive: false,
    });

    // Log the moderation action
    await ctx.db.insert("moderationLogs", {
      streamId: args.streamId,
      moderatorId: moderator.userId,
      targetUserId: args.targetUserId,
      action: "ban",
      reason: args.reason,
      timestamp: Date.now(),
    });

    return { success: true };
  },
});

export const getModerationLogs = query({
  args: {
    streamId: v.id("streams"),
    targetUserId: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Basic permission check for queries - just check if user is a participant
    const participant = await ctx.db
      .query("streamParticipants")
      .withIndex("by_user_stream", (q) => 
        q.eq("userId", identity.subject).eq("streamId", args.streamId)
      )
      .first();

    if (!participant) {
      throw new Error("Not a participant in this stream");
    }

    let query = ctx.db
      .query("moderationLogs")
      .withIndex("by_stream", (q) => q.eq("streamId", args.streamId))
      .order("desc");

    if (args.targetUserId) {
      query = ctx.db
        .query("moderationLogs")
        .withIndex("by_target", (q) => q.eq("targetUserId", args.targetUserId!))
        .filter((q) => q.eq(q.field("streamId"), args.streamId))
        .order("desc");
    }

    const logs = await query.take(args.limit || 50);

    // Get user info for each log entry
    const logsWithUsers = await Promise.all(
      logs.map(async (log) => {
        const moderator = await ctx.db
          .query("users")
          .withIndex("by_user_id", (q) => q.eq("userId", log.moderatorId))
          .first();

        const target = await ctx.db
          .query("users")
          .withIndex("by_user_id", (q) => q.eq("userId", log.targetUserId))
          .first();

        return {
          ...log,
          moderator: moderator ? {
            username: moderator.username,
            globalRole: moderator.globalRole,
          } : null,
          target: target ? {
            username: target.username,
            globalRole: target.globalRole,
          } : null,
        };
      })
    );

    return logsWithUsers;
  },
});

export const getActiveModerationsForUser = query({
  args: {
    streamId: v.id("streams"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    // Get recent moderation actions for this user
    const recentLogs = await ctx.db
      .query("moderationLogs")
      .withIndex("by_target", (q) => q.eq("targetUserId", args.userId))
      .filter((q) => q.eq(q.field("streamId"), args.streamId))
      .order("desc")
      .take(10);

    // Find active moderation states
    const activeModerations = {
      isMuted: false,
      muteExpires: null as number | null,
      isTimedOut: false,
      timeoutExpires: null as number | null,
      isBanned: false,
    };

    const now = Date.now();

    for (const log of recentLogs) {
      if (log.action === "mute" && !activeModerations.isMuted) {
        if (!log.duration) {
          activeModerations.isMuted = true;
        } else {
          const expiresAt = log.timestamp + (log.duration * 60 * 1000);
          if (now < expiresAt) {
            activeModerations.isMuted = true;
            activeModerations.muteExpires = expiresAt;
          }
        }
      } else if (log.action === "unmute") {
        break; // Stop checking for mutes
      } else if (log.action === "timeout" && !activeModerations.isTimedOut) {
        const expiresAt = log.timestamp + (log.duration! * 60 * 1000);
        if (now < expiresAt) {
          activeModerations.isTimedOut = true;
          activeModerations.timeoutExpires = expiresAt;
        }
      } else if (log.action === "ban") {
        activeModerations.isBanned = true;
        break;
      }
    }

    return activeModerations;
  },
});