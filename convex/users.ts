import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createUser = mutation({
  args: {
    username: v.string(),
    email: v.string(),
    globalRole: v.optional(v.union(v.literal("master"), v.literal("admin"), v.literal("user"))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
      .first();

    if (existingUser) {
      return existingUser._id;
    }

    // Create new user with default role
    const userId = await ctx.db.insert("users", {
      userId: identity.subject,
      username: args.username,
      email: args.email,
      globalRole: args.globalRole || "user",
      isBanned: false,
    });

    return userId;
  },
});

export const getUser = query({
  args: {
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const targetUserId = args.userId || identity.subject;
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", targetUserId))
      .first();

    return user;
  },
});

export const getCurrentUser = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
      .first();

    return user;
  },
});

export const updateUserRole = mutation({
  args: {
    targetUserId: v.string(),
    newRole: v.union(v.literal("master"), v.literal("admin"), v.literal("user")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get current user's role
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
      .first();

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Check permissions: only master can modify roles, admin cannot modify master roles
    if (currentUser.globalRole !== "master" && currentUser.globalRole !== "admin") {
      throw new Error("Insufficient permissions");
    }

    // Get target user
    const targetUser = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", args.targetUserId))
      .first();

    if (!targetUser) {
      throw new Error("Target user not found");
    }

    // Admins cannot modify master roles
    if (currentUser.globalRole === "admin" && targetUser.globalRole === "master") {
      throw new Error("Cannot modify master role");
    }

    // Update the role
    await ctx.db.patch(targetUser._id, {
      globalRole: args.newRole,
    });

    return { success: true };
  },
});

export const banUser = mutation({
  args: {
    targetUserId: v.string(),
    banned: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get current user's role
    const currentUser = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", identity.subject))
      .first();

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Check permissions
    if (currentUser.globalRole !== "master" && currentUser.globalRole !== "admin") {
      throw new Error("Insufficient permissions");
    }

    // Get target user
    const targetUser = await ctx.db
      .query("users")
      .withIndex("by_user_id", (q) => q.eq("userId", args.targetUserId))
      .first();

    if (!targetUser) {
      throw new Error("Target user not found");
    }

    // Admins cannot ban masters
    if (currentUser.globalRole === "admin" && targetUser.globalRole === "master") {
      throw new Error("Cannot ban master user");
    }

    // Update ban status
    await ctx.db.patch(targetUser._id, {
      isBanned: args.banned,
    });

    return { success: true };
  },
});