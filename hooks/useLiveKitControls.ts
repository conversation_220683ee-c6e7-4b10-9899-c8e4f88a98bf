"use client";

import { useState, useEffect, useCallback } from 'react';
import {
  useLocalParticipant,
  useRoomContext,
  useTracks
} from '@livekit/components-react';
import { 
  Track, 
  LocalTrack, 
  LocalVideoTrack, 
  LocalAudioTrack,
  TrackPublication,
  Room,
  RoomEvent
} from 'livekit-client';

export interface MediaControlsState {
  isMuted: boolean;
  isVideoOff: boolean;
  isScreenSharing: boolean;
  isRecording: boolean;
  isLoading: {
    audio: boolean;
    video: boolean;
    screenShare: boolean;
    recording: boolean;
  };
  error: string | null;
}

export interface MediaControlsActions {
  toggleMute: () => Promise<void>;
  toggleVideo: () => Promise<void>;
  toggleScreenShare: () => Promise<void>;
  toggleRecording: () => Promise<void>;
  clearError: () => void;
}

export function useLiveKitControls(): MediaControlsState & MediaControlsActions {
  const roomContext = useRoomContext();
  const room = roomContext;
  const { localParticipant } = useLocalParticipant();
  
  const [state, setState] = useState<MediaControlsState>({
    isMuted: false,
    isVideoOff: false,
    isScreenSharing: false,
    isRecording: false,
    isLoading: {
      audio: false,
      video: false,
      screenShare: false,
      recording: false,
    },
    error: null,
  });

  // Get current tracks
  const tracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: false },
    { source: Track.Source.Microphone, withPlaceholder: false },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Recording will be handled through room API

  // Update state based on actual track states
  useEffect(() => {
    if (!localParticipant) return;

    const audioTrack = localParticipant.getTrackPublication(Track.Source.Microphone);
    const videoTrack = localParticipant.getTrackPublication(Track.Source.Camera);
    const screenTrack = localParticipant.getTrackPublication(Track.Source.ScreenShare);

    setState(prev => ({
      ...prev,
      isMuted: audioTrack ? audioTrack.isMuted : true,
      isVideoOff: videoTrack ? videoTrack.isMuted : true,
      isScreenSharing: screenTrack ? !screenTrack.isMuted : false,
    }));
  }, [localParticipant, tracks]);

  // Listen for recording state changes
  useEffect(() => {
    if (!room) return;

    const handleRecordingStateChanged = (recording: boolean) => {
      setState(prev => ({
        ...prev,
        isRecording: recording,
        isLoading: { ...prev.isLoading, recording: false }
      }));
    };

    // Listen for recording events
    room.on(RoomEvent.RecordingStatusChanged, handleRecordingStateChanged);

    return () => {
      room.off(RoomEvent.RecordingStatusChanged, handleRecordingStateChanged);
    };
  }, [room]);

  const setLoading = useCallback((type: keyof MediaControlsState['isLoading'], loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: { ...prev.isLoading, [type]: loading }
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  const toggleMute = useCallback(async () => {
    if (!localParticipant) {
      setError('Not connected to room');
      return;
    }

    try {
      setLoading('audio', true);
      setError(null);

      const audioTrack = localParticipant.getTrackPublication(Track.Source.Microphone);
      
      if (audioTrack) {
        // Toggle existing track
        await localParticipant.setMicrophoneEnabled(audioTrack.isMuted);
      } else {
        // Enable microphone if no track exists
        await localParticipant.setMicrophoneEnabled(true);
      }
    } catch (error) {
      console.error('Failed to toggle microphone:', error);
      setError('Failed to toggle microphone');
    } finally {
      setLoading('audio', false);
    }
  }, [localParticipant, setLoading, setError]);

  const toggleVideo = useCallback(async () => {
    if (!localParticipant) {
      setError('Not connected to room');
      return;
    }

    try {
      setLoading('video', true);
      setError(null);

      const videoTrack = localParticipant.getTrackPublication(Track.Source.Camera);
      
      if (videoTrack) {
        // Toggle existing track
        await localParticipant.setCameraEnabled(videoTrack.isMuted);
      } else {
        // Enable camera if no track exists
        await localParticipant.setCameraEnabled(true);
      }
    } catch (error) {
      console.error('Failed to toggle camera:', error);
      setError('Failed to toggle camera');
    } finally {
      setLoading('video', false);
    }
  }, [localParticipant, setLoading, setError]);

  const toggleScreenShare = useCallback(async () => {
    if (!localParticipant) {
      setError('Not connected to room');
      return;
    }

    try {
      setLoading('screenShare', true);
      setError(null);

      const screenTrack = localParticipant.getTrackPublication(Track.Source.ScreenShare);
      
      if (screenTrack && !screenTrack.isMuted) {
        // Stop screen sharing
        await localParticipant.setScreenShareEnabled(false);
      } else {
        // Start screen sharing
        await localParticipant.setScreenShareEnabled(true);
      }
    } catch (error) {
      console.error('Failed to toggle screen share:', error);
      setError('Failed to toggle screen share');
    } finally {
      setLoading('screenShare', false);
    }
  }, [localParticipant, setLoading, setError]);

  const toggleRecording = useCallback(async () => {
    if (!room) {
      setError('Not connected to room');
      return;
    }

    try {
      setLoading('recording', true);
      setError(null);

      const roomName = room.name;

      if (state.isRecording) {
        // Stop recording - would need to get egressId from room state
        console.log('Stopping recording for room:', roomName);
        // TODO: Get egressId from room metadata and call stopRecording
        setState(prev => ({ ...prev, isRecording: false }));
      } else {
        // Start recording
        console.log('Starting recording for room:', roomName);
        // TODO: Call startRecording action
        setState(prev => ({ ...prev, isRecording: true }));
      }
    } catch (error) {
      console.error('Failed to toggle recording:', error);
      setError('Failed to toggle recording');
    } finally {
      setLoading('recording', false);
    }
  }, [room, state.isRecording, setLoading, setError]);

  return {
    ...state,
    toggleMute,
    toggleVideo,
    toggleScreenShare,
    toggleRecording,
    clearError,
  };
}
